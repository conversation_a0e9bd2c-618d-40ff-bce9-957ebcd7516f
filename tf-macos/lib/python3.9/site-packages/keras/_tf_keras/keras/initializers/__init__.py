"""DO NOT EDIT.

This file was autogenerated. Do not edit it by hand,
since your modifications would be overwritten.
"""

from keras.src.initializers import deserialize as deserialize
from keras.src.initializers import get as get
from keras.src.initializers import serialize as serialize
from keras.src.initializers.constant_initializers import STFT as STFT
from keras.src.initializers.constant_initializers import STFT as STFTInitializer
from keras.src.initializers.constant_initializers import STFT as stft
from keras.src.initializers.constant_initializers import Constant as Constant
from keras.src.initializers.constant_initializers import Constant as constant
from keras.src.initializers.constant_initializers import Identity as Identity
from keras.src.initializers.constant_initializers import (
    Identity as IdentityInitializer,
)
from keras.src.initializers.constant_initializers import Identity as identity
from keras.src.initializers.constant_initializers import Ones as Ones
from keras.src.initializers.constant_initializers import Ones as ones
from keras.src.initializers.constant_initializers import Zeros as Zeros
from keras.src.initializers.constant_initializers import Zeros as zeros
from keras.src.initializers.initializer import Initializer as Initializer
from keras.src.initializers.random_initializers import (
    GlorotNormal as GlorotNormal,
)
from keras.src.initializers.random_initializers import (
    GlorotNormal as glorot_normal,
)
from keras.src.initializers.random_initializers import (
    GlorotUniform as GlorotUniform,
)
from keras.src.initializers.random_initializers import (
    GlorotUniform as glorot_uniform,
)
from keras.src.initializers.random_initializers import HeNormal as HeNormal
from keras.src.initializers.random_initializers import HeNormal as he_normal
from keras.src.initializers.random_initializers import HeUniform as HeUniform
from keras.src.initializers.random_initializers import HeUniform as he_uniform
from keras.src.initializers.random_initializers import (
    LecunNormal as LecunNormal,
)
from keras.src.initializers.random_initializers import (
    LecunNormal as lecun_normal,
)
from keras.src.initializers.random_initializers import (
    LecunUniform as LecunUniform,
)
from keras.src.initializers.random_initializers import (
    LecunUniform as lecun_uniform,
)
from keras.src.initializers.random_initializers import Orthogonal as Orthogonal
from keras.src.initializers.random_initializers import (
    Orthogonal as OrthogonalInitializer,
)
from keras.src.initializers.random_initializers import Orthogonal as orthogonal
from keras.src.initializers.random_initializers import (
    RandomNormal as RandomNormal,
)
from keras.src.initializers.random_initializers import (
    RandomNormal as random_normal,
)
from keras.src.initializers.random_initializers import (
    RandomUniform as RandomUniform,
)
from keras.src.initializers.random_initializers import (
    RandomUniform as random_uniform,
)
from keras.src.initializers.random_initializers import (
    TruncatedNormal as TruncatedNormal,
)
from keras.src.initializers.random_initializers import (
    TruncatedNormal as truncated_normal,
)
from keras.src.initializers.random_initializers import (
    VarianceScaling as VarianceScaling,
)
from keras.src.initializers.random_initializers import (
    VarianceScaling as variance_scaling,
)

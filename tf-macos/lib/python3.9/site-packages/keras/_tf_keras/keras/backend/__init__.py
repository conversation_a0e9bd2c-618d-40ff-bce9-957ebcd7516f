"""DO NOT EDIT.

This file was autogenerated. Do not edit it by hand,
since your modifications would be overwritten.
"""

from keras.src.backend.common.dtypes import result_type as result_type
from keras.src.backend.common.global_state import clear_session as clear_session
from keras.src.backend.common.keras_tensor import (
    is_keras_tensor as is_keras_tensor,
)
from keras.src.backend.common.variables import is_float_dtype as is_float_dtype
from keras.src.backend.common.variables import is_int_dtype as is_int_dtype
from keras.src.backend.common.variables import (
    standardize_dtype as standardize_dtype,
)
from keras.src.backend.config import backend as backend
from keras.src.backend.config import epsilon as epsilon
from keras.src.backend.config import floatx as floatx
from keras.src.backend.config import image_data_format as image_data_format
from keras.src.backend.config import set_epsilon as set_epsilon
from keras.src.backend.config import set_floatx as set_floatx
from keras.src.backend.config import (
    set_image_data_format as set_image_data_format,
)
from keras.src.legacy.backend import abs as abs
from keras.src.legacy.backend import all as all
from keras.src.legacy.backend import any as any
from keras.src.legacy.backend import arange as arange
from keras.src.legacy.backend import argmax as argmax
from keras.src.legacy.backend import argmin as argmin
from keras.src.legacy.backend import batch_dot as batch_dot
from keras.src.legacy.backend import batch_flatten as batch_flatten
from keras.src.legacy.backend import batch_get_value as batch_get_value
from keras.src.legacy.backend import batch_normalization as batch_normalization
from keras.src.legacy.backend import batch_set_value as batch_set_value
from keras.src.legacy.backend import bias_add as bias_add
from keras.src.legacy.backend import binary_crossentropy as binary_crossentropy
from keras.src.legacy.backend import (
    binary_focal_crossentropy as binary_focal_crossentropy,
)
from keras.src.legacy.backend import cast as cast
from keras.src.legacy.backend import cast_to_floatx as cast_to_floatx
from keras.src.legacy.backend import (
    categorical_crossentropy as categorical_crossentropy,
)
from keras.src.legacy.backend import (
    categorical_focal_crossentropy as categorical_focal_crossentropy,
)
from keras.src.legacy.backend import clip as clip
from keras.src.legacy.backend import concatenate as concatenate
from keras.src.legacy.backend import constant as constant
from keras.src.legacy.backend import conv1d as conv1d
from keras.src.legacy.backend import conv2d as conv2d
from keras.src.legacy.backend import conv2d_transpose as conv2d_transpose
from keras.src.legacy.backend import conv3d as conv3d
from keras.src.legacy.backend import cos as cos
from keras.src.legacy.backend import count_params as count_params
from keras.src.legacy.backend import ctc_batch_cost as ctc_batch_cost
from keras.src.legacy.backend import ctc_decode as ctc_decode
from keras.src.legacy.backend import (
    ctc_label_dense_to_sparse as ctc_label_dense_to_sparse,
)
from keras.src.legacy.backend import cumprod as cumprod
from keras.src.legacy.backend import cumsum as cumsum
from keras.src.legacy.backend import depthwise_conv2d as depthwise_conv2d
from keras.src.legacy.backend import dot as dot
from keras.src.legacy.backend import dropout as dropout
from keras.src.legacy.backend import dtype as dtype
from keras.src.legacy.backend import elu as elu
from keras.src.legacy.backend import equal as equal
from keras.src.legacy.backend import eval as eval
from keras.src.legacy.backend import exp as exp
from keras.src.legacy.backend import expand_dims as expand_dims
from keras.src.legacy.backend import eye as eye
from keras.src.legacy.backend import flatten as flatten
from keras.src.legacy.backend import foldl as foldl
from keras.src.legacy.backend import foldr as foldr
from keras.src.legacy.backend import gather as gather
from keras.src.legacy.backend import get_value as get_value
from keras.src.legacy.backend import gradients as gradients
from keras.src.legacy.backend import greater as greater
from keras.src.legacy.backend import greater_equal as greater_equal
from keras.src.legacy.backend import hard_sigmoid as hard_sigmoid
from keras.src.legacy.backend import in_top_k as in_top_k
from keras.src.legacy.backend import int_shape as int_shape
from keras.src.legacy.backend import is_sparse as is_sparse
from keras.src.legacy.backend import l2_normalize as l2_normalize
from keras.src.legacy.backend import less as less
from keras.src.legacy.backend import less_equal as less_equal
from keras.src.legacy.backend import log as log
from keras.src.legacy.backend import map_fn as map_fn
from keras.src.legacy.backend import max as max
from keras.src.legacy.backend import maximum as maximum
from keras.src.legacy.backend import mean as mean
from keras.src.legacy.backend import min as min
from keras.src.legacy.backend import minimum as minimum
from keras.src.legacy.backend import (
    moving_average_update as moving_average_update,
)
from keras.src.legacy.backend import name_scope as name_scope
from keras.src.legacy.backend import ndim as ndim
from keras.src.legacy.backend import not_equal as not_equal
from keras.src.legacy.backend import one_hot as one_hot
from keras.src.legacy.backend import ones as ones
from keras.src.legacy.backend import ones_like as ones_like
from keras.src.legacy.backend import permute_dimensions as permute_dimensions
from keras.src.legacy.backend import pool2d as pool2d
from keras.src.legacy.backend import pool3d as pool3d
from keras.src.legacy.backend import pow as pow
from keras.src.legacy.backend import prod as prod
from keras.src.legacy.backend import random_bernoulli as random_bernoulli
from keras.src.legacy.backend import random_normal as random_normal
from keras.src.legacy.backend import (
    random_normal_variable as random_normal_variable,
)
from keras.src.legacy.backend import random_uniform as random_uniform
from keras.src.legacy.backend import (
    random_uniform_variable as random_uniform_variable,
)
from keras.src.legacy.backend import relu as relu
from keras.src.legacy.backend import repeat as repeat
from keras.src.legacy.backend import repeat_elements as repeat_elements
from keras.src.legacy.backend import reshape as reshape
from keras.src.legacy.backend import resize_images as resize_images
from keras.src.legacy.backend import resize_volumes as resize_volumes
from keras.src.legacy.backend import reverse as reverse
from keras.src.legacy.backend import rnn as rnn
from keras.src.legacy.backend import round as round
from keras.src.legacy.backend import separable_conv2d as separable_conv2d
from keras.src.legacy.backend import set_value as set_value
from keras.src.legacy.backend import shape as shape
from keras.src.legacy.backend import sigmoid as sigmoid
from keras.src.legacy.backend import sign as sign
from keras.src.legacy.backend import sin as sin
from keras.src.legacy.backend import softmax as softmax
from keras.src.legacy.backend import softplus as softplus
from keras.src.legacy.backend import softsign as softsign
from keras.src.legacy.backend import (
    sparse_categorical_crossentropy as sparse_categorical_crossentropy,
)
from keras.src.legacy.backend import spatial_2d_padding as spatial_2d_padding
from keras.src.legacy.backend import spatial_3d_padding as spatial_3d_padding
from keras.src.legacy.backend import sqrt as sqrt
from keras.src.legacy.backend import square as square
from keras.src.legacy.backend import squeeze as squeeze
from keras.src.legacy.backend import stack as stack
from keras.src.legacy.backend import std as std
from keras.src.legacy.backend import stop_gradient as stop_gradient
from keras.src.legacy.backend import sum as sum
from keras.src.legacy.backend import switch as switch
from keras.src.legacy.backend import tanh as tanh
from keras.src.legacy.backend import temporal_padding as temporal_padding
from keras.src.legacy.backend import tile as tile
from keras.src.legacy.backend import to_dense as to_dense
from keras.src.legacy.backend import transpose as transpose
from keras.src.legacy.backend import truncated_normal as truncated_normal
from keras.src.legacy.backend import update as update
from keras.src.legacy.backend import update_add as update_add
from keras.src.legacy.backend import update_sub as update_sub
from keras.src.legacy.backend import var as var
from keras.src.legacy.backend import variable as variable
from keras.src.legacy.backend import zeros as zeros
from keras.src.legacy.backend import zeros_like as zeros_like
from keras.src.utils.naming import get_uid as get_uid

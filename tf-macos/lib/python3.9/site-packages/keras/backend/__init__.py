"""DO NOT EDIT.

This file was autogenerated. Do not edit it by hand,
since your modifications would be overwritten.
"""

from keras.src.backend.common.dtypes import result_type as result_type
from keras.src.backend.common.global_state import clear_session as clear_session
from keras.src.backend.common.keras_tensor import (
    is_keras_tensor as is_keras_tensor,
)
from keras.src.backend.common.variables import is_float_dtype as is_float_dtype
from keras.src.backend.common.variables import is_int_dtype as is_int_dtype
from keras.src.backend.common.variables import (
    standardize_dtype as standardize_dtype,
)
from keras.src.backend.config import backend as backend
from keras.src.backend.config import epsilon as epsilon
from keras.src.backend.config import floatx as floatx
from keras.src.backend.config import image_data_format as image_data_format
from keras.src.backend.config import set_epsilon as set_epsilon
from keras.src.backend.config import set_floatx as set_floatx
from keras.src.backend.config import (
    set_image_data_format as set_image_data_format,
)
from keras.src.utils.naming import get_uid as get_uid

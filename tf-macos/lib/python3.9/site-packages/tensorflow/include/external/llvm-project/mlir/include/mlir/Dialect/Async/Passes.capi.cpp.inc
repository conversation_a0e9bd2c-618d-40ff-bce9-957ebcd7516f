/* Autogenerated by mlir-tblgen; don't manually edit. */
//===----------------------------------------------------------------------===//
// Async Group Registration
//===----------------------------------------------------------------------===//

void mlirRegisterAsyncPasses(void) {
  registerAsyncPasses();
}

MlirPass mlirCreateAsyncAsyncFuncToAsyncRuntime(void) {
  return wrap(mlir::createAsyncFuncToAsyncRuntimePass().release());
}
void mlirRegisterAsyncAsyncFuncToAsyncRuntime(void) {
  registerAsyncFuncToAsyncRuntime();
}


MlirPass mlirCreateAsyncAsyncParallelFor(void) {
  return wrap(mlir::createAsyncParallelForPass().release());
}
void mlirRegisterAsyncAsyncParallelFor(void) {
  registerAsyncParallelFor();
}


MlirPass mlirCreateAsyncAsyncRuntimePolicyBasedRefCounting(void) {
  return wrap(mlir::createAsyncRuntimePolicyBasedRefCountingPass().release());
}
void mlirRegisterAsyncAsyncRuntimePolicyBasedRefCounting(void) {
  registerAsyncRuntimePolicyBasedRefCounting();
}


MlirPass mlirCreateAsyncAsyncRuntimeRefCounting(void) {
  return wrap(mlir::createAsyncRuntimeRefCountingPass().release());
}
void mlirRegisterAsyncAsyncRuntimeRefCounting(void) {
  registerAsyncRuntimeRefCounting();
}


MlirPass mlirCreateAsyncAsyncRuntimeRefCountingOpt(void) {
  return wrap(mlir::createAsyncRuntimeRefCountingOptPass().release());
}
void mlirRegisterAsyncAsyncRuntimeRefCountingOpt(void) {
  registerAsyncRuntimeRefCountingOpt();
}


MlirPass mlirCreateAsyncAsyncToAsyncRuntime(void) {
  return wrap(mlir::createAsyncToAsyncRuntimePass().release());
}
void mlirRegisterAsyncAsyncToAsyncRuntime(void) {
  registerAsyncToAsyncRuntime();
}


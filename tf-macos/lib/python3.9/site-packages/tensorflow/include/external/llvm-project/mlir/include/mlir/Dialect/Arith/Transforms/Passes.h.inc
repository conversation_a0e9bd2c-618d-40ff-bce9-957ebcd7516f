/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_ARITHBUFFERIZEPASS
#define GEN_PASS_DECL_ARITHEMULATEUNSUPPORTEDFLOATS
#define GEN_PASS_DECL_ARITHEMULATEWIDEINT
#define GEN_PASS_DECL_ARITHEXPANDOPSPASS
#define GEN_PASS_DECL_ARITHINTNARROWING
#define GEN_PASS_DECL_ARITHINTRANGEOPTS
#define GEN_PASS_DECL_ARITHUNSIGNEDWHENEQUIVALENT
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// ArithBufferizePass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_ARITHBUFFERIZEPASS
struct ArithBufferizePassOptions {
  unsigned alignment = 0;
};
std::unique_ptr<::mlir::Pass> createArithBufferizePass();
std::unique_ptr<::mlir::Pass> createArithBufferizePass(const ArithBufferizePassOptions &options);
#undef GEN_PASS_DECL_ARITHBUFFERIZEPASS
#endif // GEN_PASS_DECL_ARITHBUFFERIZEPASS
#ifdef GEN_PASS_DEF_ARITHBUFFERIZEPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createArithBufferizePass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createArithBufferizePass(const ArithBufferizePassOptions &options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class ArithBufferizePassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ArithBufferizePassBase;

  ArithBufferizePassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ArithBufferizePassBase(const ArithBufferizePassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("arith-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "arith-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Bufferize Arith dialect ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ArithBufferizePass");
  }
  ::llvm::StringRef getName() const override { return "ArithBufferizePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ArithBufferizePassBase<DerivedT>)

  ArithBufferizePassBase(const ArithBufferizePassOptions &options) : ArithBufferizePassBase() {
    alignment = options.alignment;
  }
protected:
  ::mlir::Pass::Option<unsigned> alignment{*this, "alignment", ::llvm::cl::desc("Create global memrefs with a specified alignment"), ::llvm::cl::init(0)};
private:

  friend std::unique_ptr<::mlir::Pass> createArithBufferizePass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createArithBufferizePass(const ArithBufferizePassOptions &options) {
    return std::make_unique<DerivedT>(options);
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createArithBufferizePass() {
  return impl::createArithBufferizePass();
}

std::unique_ptr<::mlir::Pass> createArithBufferizePass(const ArithBufferizePassOptions &options) {
  return impl::createArithBufferizePass(options);
}
#undef GEN_PASS_DEF_ARITHBUFFERIZEPASS
#endif // GEN_PASS_DEF_ARITHBUFFERIZEPASS

//===----------------------------------------------------------------------===//
// ArithEmulateUnsupportedFloats
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_ARITHEMULATEUNSUPPORTEDFLOATS
struct ArithEmulateUnsupportedFloatsOptions {
  ::llvm::ArrayRef<std::string> sourceTypeStrs;
  std::string targetTypeStr = "f32";
};
std::unique_ptr<::mlir::Pass> createArithEmulateUnsupportedFloats();
std::unique_ptr<::mlir::Pass> createArithEmulateUnsupportedFloats(const ArithEmulateUnsupportedFloatsOptions &options);
#undef GEN_PASS_DECL_ARITHEMULATEUNSUPPORTEDFLOATS
#endif // GEN_PASS_DECL_ARITHEMULATEUNSUPPORTEDFLOATS
#ifdef GEN_PASS_DEF_ARITHEMULATEUNSUPPORTEDFLOATS

namespace impl {
  std::unique_ptr<::mlir::Pass> createArithEmulateUnsupportedFloats();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createArithEmulateUnsupportedFloats(const ArithEmulateUnsupportedFloatsOptions &options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class ArithEmulateUnsupportedFloatsBase : public ::mlir::OperationPass<> {
public:
  using Base = ArithEmulateUnsupportedFloatsBase;

  ArithEmulateUnsupportedFloatsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ArithEmulateUnsupportedFloatsBase(const ArithEmulateUnsupportedFloatsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("arith-emulate-unsupported-floats");
  }
  ::llvm::StringRef getArgument() const override { return "arith-emulate-unsupported-floats"; }

  ::llvm::StringRef getDescription() const override { return "Emulate operations on unsupported floats with extf/truncf"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ArithEmulateUnsupportedFloats");
  }
  ::llvm::StringRef getName() const override { return "ArithEmulateUnsupportedFloats"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<vector::VectorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ArithEmulateUnsupportedFloatsBase<DerivedT>)

  ArithEmulateUnsupportedFloatsBase(const ArithEmulateUnsupportedFloatsOptions &options) : ArithEmulateUnsupportedFloatsBase() {
    sourceTypeStrs = options.sourceTypeStrs;
    targetTypeStr = options.targetTypeStr;
  }
protected:
  ::mlir::Pass::ListOption<std::string> sourceTypeStrs{*this, "source-types", ::llvm::cl::desc("MLIR types without arithmetic support on a given target")};
  ::mlir::Pass::Option<std::string> targetTypeStr{*this, "target-type", ::llvm::cl::desc("MLIR type to convert the unsupported source types to"), ::llvm::cl::init("f32")};
private:

  friend std::unique_ptr<::mlir::Pass> createArithEmulateUnsupportedFloats() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createArithEmulateUnsupportedFloats(const ArithEmulateUnsupportedFloatsOptions &options) {
    return std::make_unique<DerivedT>(options);
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createArithEmulateUnsupportedFloats() {
  return impl::createArithEmulateUnsupportedFloats();
}

std::unique_ptr<::mlir::Pass> createArithEmulateUnsupportedFloats(const ArithEmulateUnsupportedFloatsOptions &options) {
  return impl::createArithEmulateUnsupportedFloats(options);
}
#undef GEN_PASS_DEF_ARITHEMULATEUNSUPPORTEDFLOATS
#endif // GEN_PASS_DEF_ARITHEMULATEUNSUPPORTEDFLOATS

//===----------------------------------------------------------------------===//
// ArithEmulateWideInt
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_ARITHEMULATEWIDEINT
struct ArithEmulateWideIntOptions {
  unsigned widestIntSupported = 32;
};
std::unique_ptr<::mlir::Pass> createArithEmulateWideInt();
std::unique_ptr<::mlir::Pass> createArithEmulateWideInt(const ArithEmulateWideIntOptions &options);
#undef GEN_PASS_DECL_ARITHEMULATEWIDEINT
#endif // GEN_PASS_DECL_ARITHEMULATEWIDEINT
#ifdef GEN_PASS_DEF_ARITHEMULATEWIDEINT

namespace impl {
  std::unique_ptr<::mlir::Pass> createArithEmulateWideInt();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createArithEmulateWideInt(const ArithEmulateWideIntOptions &options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class ArithEmulateWideIntBase : public ::mlir::OperationPass<> {
public:
  using Base = ArithEmulateWideIntBase;

  ArithEmulateWideIntBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ArithEmulateWideIntBase(const ArithEmulateWideIntBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("arith-emulate-wide-int");
  }
  ::llvm::StringRef getArgument() const override { return "arith-emulate-wide-int"; }

  ::llvm::StringRef getDescription() const override { return "Emulate 2*N-bit integer operations using N-bit operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ArithEmulateWideInt");
  }
  ::llvm::StringRef getName() const override { return "ArithEmulateWideInt"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<vector::VectorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ArithEmulateWideIntBase<DerivedT>)

  ArithEmulateWideIntBase(const ArithEmulateWideIntOptions &options) : ArithEmulateWideIntBase() {
    widestIntSupported = options.widestIntSupported;
  }
protected:
  ::mlir::Pass::Option<unsigned> widestIntSupported{*this, "widest-int-supported", ::llvm::cl::desc("Widest integer type supported by the target"), ::llvm::cl::init(32)};
private:

  friend std::unique_ptr<::mlir::Pass> createArithEmulateWideInt() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createArithEmulateWideInt(const ArithEmulateWideIntOptions &options) {
    return std::make_unique<DerivedT>(options);
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createArithEmulateWideInt() {
  return impl::createArithEmulateWideInt();
}

std::unique_ptr<::mlir::Pass> createArithEmulateWideInt(const ArithEmulateWideIntOptions &options) {
  return impl::createArithEmulateWideInt(options);
}
#undef GEN_PASS_DEF_ARITHEMULATEWIDEINT
#endif // GEN_PASS_DEF_ARITHEMULATEWIDEINT

//===----------------------------------------------------------------------===//
// ArithExpandOpsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_ARITHEXPANDOPSPASS
struct ArithExpandOpsPassOptions {
  bool includeBf16 = false;
};
std::unique_ptr<::mlir::Pass> createArithExpandOpsPass();
std::unique_ptr<::mlir::Pass> createArithExpandOpsPass(const ArithExpandOpsPassOptions &options);
#undef GEN_PASS_DECL_ARITHEXPANDOPSPASS
#endif // GEN_PASS_DECL_ARITHEXPANDOPSPASS
#ifdef GEN_PASS_DEF_ARITHEXPANDOPSPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createArithExpandOpsPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createArithExpandOpsPass(const ArithExpandOpsPassOptions &options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class ArithExpandOpsPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ArithExpandOpsPassBase;

  ArithExpandOpsPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ArithExpandOpsPassBase(const ArithExpandOpsPassBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("arith-expand");
  }
  ::llvm::StringRef getArgument() const override { return "arith-expand"; }

  ::llvm::StringRef getDescription() const override { return "Legalize Arith ops to be convertible to LLVM."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ArithExpandOpsPass");
  }
  ::llvm::StringRef getName() const override { return "ArithExpandOpsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<vector::VectorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ArithExpandOpsPassBase<DerivedT>)

  ArithExpandOpsPassBase(const ArithExpandOpsPassOptions &options) : ArithExpandOpsPassBase() {
    includeBf16 = options.includeBf16;
  }
protected:
  ::mlir::Pass::Option<bool> includeBf16{*this, "include-bf16", ::llvm::cl::desc("Enable the BF16 expansion patterns"), ::llvm::cl::init(false)};
private:

  friend std::unique_ptr<::mlir::Pass> createArithExpandOpsPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createArithExpandOpsPass(const ArithExpandOpsPassOptions &options) {
    return std::make_unique<DerivedT>(options);
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createArithExpandOpsPass() {
  return impl::createArithExpandOpsPass();
}

std::unique_ptr<::mlir::Pass> createArithExpandOpsPass(const ArithExpandOpsPassOptions &options) {
  return impl::createArithExpandOpsPass(options);
}
#undef GEN_PASS_DEF_ARITHEXPANDOPSPASS
#endif // GEN_PASS_DEF_ARITHEXPANDOPSPASS

//===----------------------------------------------------------------------===//
// ArithIntNarrowing
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_ARITHINTNARROWING
struct ArithIntNarrowingOptions {
  ::llvm::ArrayRef<unsigned> bitwidthsSupported;
};
std::unique_ptr<::mlir::Pass> createArithIntNarrowing();
std::unique_ptr<::mlir::Pass> createArithIntNarrowing(const ArithIntNarrowingOptions &options);
#undef GEN_PASS_DECL_ARITHINTNARROWING
#endif // GEN_PASS_DECL_ARITHINTNARROWING
#ifdef GEN_PASS_DEF_ARITHINTNARROWING

namespace impl {
  std::unique_ptr<::mlir::Pass> createArithIntNarrowing();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createArithIntNarrowing(const ArithIntNarrowingOptions &options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class ArithIntNarrowingBase : public ::mlir::OperationPass<> {
public:
  using Base = ArithIntNarrowingBase;

  ArithIntNarrowingBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ArithIntNarrowingBase(const ArithIntNarrowingBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("arith-int-narrowing");
  }
  ::llvm::StringRef getArgument() const override { return "arith-int-narrowing"; }

  ::llvm::StringRef getDescription() const override { return "Reduce integer operation bitwidth"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ArithIntNarrowing");
  }
  ::llvm::StringRef getName() const override { return "ArithIntNarrowing"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<vector::VectorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ArithIntNarrowingBase<DerivedT>)

  ArithIntNarrowingBase(const ArithIntNarrowingOptions &options) : ArithIntNarrowingBase() {
    bitwidthsSupported = options.bitwidthsSupported;
  }
protected:
  ::mlir::Pass::ListOption<unsigned> bitwidthsSupported{*this, "int-bitwidths-supported", ::llvm::cl::desc("Integer bitwidths supported")};
private:

  friend std::unique_ptr<::mlir::Pass> createArithIntNarrowing() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createArithIntNarrowing(const ArithIntNarrowingOptions &options) {
    return std::make_unique<DerivedT>(options);
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createArithIntNarrowing() {
  return impl::createArithIntNarrowing();
}

std::unique_ptr<::mlir::Pass> createArithIntNarrowing(const ArithIntNarrowingOptions &options) {
  return impl::createArithIntNarrowing(options);
}
#undef GEN_PASS_DEF_ARITHINTNARROWING
#endif // GEN_PASS_DEF_ARITHINTNARROWING

//===----------------------------------------------------------------------===//
// ArithIntRangeOpts
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_ARITHINTRANGEOPTS
std::unique_ptr<::mlir::Pass> createArithIntRangeOpts();
#undef GEN_PASS_DECL_ARITHINTRANGEOPTS
#endif // GEN_PASS_DECL_ARITHINTRANGEOPTS
#ifdef GEN_PASS_DEF_ARITHINTRANGEOPTS

namespace impl {
  std::unique_ptr<::mlir::Pass> createArithIntRangeOpts();
} // namespace impl
namespace impl {

template <typename DerivedT>
class ArithIntRangeOptsBase : public ::mlir::OperationPass<> {
public:
  using Base = ArithIntRangeOptsBase;

  ArithIntRangeOptsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ArithIntRangeOptsBase(const ArithIntRangeOptsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("int-range-optimizations");
  }
  ::llvm::StringRef getArgument() const override { return "int-range-optimizations"; }

  ::llvm::StringRef getDescription() const override { return "Do optimizations based on integer range analysis"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ArithIntRangeOpts");
  }
  ::llvm::StringRef getName() const override { return "ArithIntRangeOpts"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ArithIntRangeOptsBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createArithIntRangeOpts() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createArithIntRangeOpts() {
  return impl::createArithIntRangeOpts();
}
#undef GEN_PASS_DEF_ARITHINTRANGEOPTS
#endif // GEN_PASS_DEF_ARITHINTRANGEOPTS

//===----------------------------------------------------------------------===//
// ArithUnsignedWhenEquivalent
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_ARITHUNSIGNEDWHENEQUIVALENT
#undef GEN_PASS_DECL_ARITHUNSIGNEDWHENEQUIVALENT
#endif // GEN_PASS_DECL_ARITHUNSIGNEDWHENEQUIVALENT
#ifdef GEN_PASS_DEF_ARITHUNSIGNEDWHENEQUIVALENT
namespace impl {

template <typename DerivedT>
class ArithUnsignedWhenEquivalentBase : public ::mlir::OperationPass<> {
public:
  using Base = ArithUnsignedWhenEquivalentBase;

  ArithUnsignedWhenEquivalentBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ArithUnsignedWhenEquivalentBase(const ArithUnsignedWhenEquivalentBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("arith-unsigned-when-equivalent");
  }
  ::llvm::StringRef getArgument() const override { return "arith-unsigned-when-equivalent"; }

  ::llvm::StringRef getDescription() const override { return "Replace signed ops with unsigned ones where they are proven equivalent"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ArithUnsignedWhenEquivalent");
  }
  ::llvm::StringRef getName() const override { return "ArithUnsignedWhenEquivalent"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ArithUnsignedWhenEquivalentBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_ARITHUNSIGNEDWHENEQUIVALENT
#endif // GEN_PASS_DEF_ARITHUNSIGNEDWHENEQUIVALENT
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// ArithBufferizePass Registration
//===----------------------------------------------------------------------===//

inline void registerArithBufferizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createArithBufferizePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerArithBufferizePassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createArithBufferizePass();
  });
}

//===----------------------------------------------------------------------===//
// ArithEmulateUnsupportedFloats Registration
//===----------------------------------------------------------------------===//

inline void registerArithEmulateUnsupportedFloats() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createArithEmulateUnsupportedFloats();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerArithEmulateUnsupportedFloatsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createArithEmulateUnsupportedFloats();
  });
}

//===----------------------------------------------------------------------===//
// ArithEmulateWideInt Registration
//===----------------------------------------------------------------------===//

inline void registerArithEmulateWideInt() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createArithEmulateWideInt();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerArithEmulateWideIntPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createArithEmulateWideInt();
  });
}

//===----------------------------------------------------------------------===//
// ArithExpandOpsPass Registration
//===----------------------------------------------------------------------===//

inline void registerArithExpandOpsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createArithExpandOpsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerArithExpandOpsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createArithExpandOpsPass();
  });
}

//===----------------------------------------------------------------------===//
// ArithIntNarrowing Registration
//===----------------------------------------------------------------------===//

inline void registerArithIntNarrowing() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createArithIntNarrowing();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerArithIntNarrowingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createArithIntNarrowing();
  });
}

//===----------------------------------------------------------------------===//
// ArithIntRangeOpts Registration
//===----------------------------------------------------------------------===//

inline void registerArithIntRangeOpts() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createArithIntRangeOpts();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerArithIntRangeOptsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createArithIntRangeOpts();
  });
}

//===----------------------------------------------------------------------===//
// ArithUnsignedWhenEquivalent Registration
//===----------------------------------------------------------------------===//

inline void registerArithUnsignedWhenEquivalent() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::arith::createArithUnsignedWhenEquivalentPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerArithUnsignedWhenEquivalentPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::arith::createArithUnsignedWhenEquivalentPass();
  });
}

//===----------------------------------------------------------------------===//
// Arith Registration
//===----------------------------------------------------------------------===//

inline void registerArithPasses() {
  registerArithBufferizePass();
  registerArithEmulateUnsupportedFloats();
  registerArithEmulateWideInt();
  registerArithExpandOpsPass();
  registerArithIntNarrowing();
  registerArithIntRangeOpts();
  registerArithUnsignedWhenEquivalent();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class ArithBufferizePassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ArithBufferizePassBase;

  ArithBufferizePassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ArithBufferizePassBase(const ArithBufferizePassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("arith-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "arith-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Bufferize Arith dialect ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ArithBufferizePass");
  }
  ::llvm::StringRef getName() const override { return "ArithBufferizePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ArithBufferizePassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<unsigned> alignment{*this, "alignment", ::llvm::cl::desc("Create global memrefs with a specified alignment"), ::llvm::cl::init(0)};
};

template <typename DerivedT>
class ArithEmulateUnsupportedFloatsBase : public ::mlir::OperationPass<> {
public:
  using Base = ArithEmulateUnsupportedFloatsBase;

  ArithEmulateUnsupportedFloatsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ArithEmulateUnsupportedFloatsBase(const ArithEmulateUnsupportedFloatsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("arith-emulate-unsupported-floats");
  }
  ::llvm::StringRef getArgument() const override { return "arith-emulate-unsupported-floats"; }

  ::llvm::StringRef getDescription() const override { return "Emulate operations on unsupported floats with extf/truncf"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ArithEmulateUnsupportedFloats");
  }
  ::llvm::StringRef getName() const override { return "ArithEmulateUnsupportedFloats"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<vector::VectorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ArithEmulateUnsupportedFloatsBase<DerivedT>)

protected:
  ::mlir::Pass::ListOption<std::string> sourceTypeStrs{*this, "source-types", ::llvm::cl::desc("MLIR types without arithmetic support on a given target")};
  ::mlir::Pass::Option<std::string> targetTypeStr{*this, "target-type", ::llvm::cl::desc("MLIR type to convert the unsupported source types to"), ::llvm::cl::init("f32")};
};

template <typename DerivedT>
class ArithEmulateWideIntBase : public ::mlir::OperationPass<> {
public:
  using Base = ArithEmulateWideIntBase;

  ArithEmulateWideIntBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ArithEmulateWideIntBase(const ArithEmulateWideIntBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("arith-emulate-wide-int");
  }
  ::llvm::StringRef getArgument() const override { return "arith-emulate-wide-int"; }

  ::llvm::StringRef getDescription() const override { return "Emulate 2*N-bit integer operations using N-bit operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ArithEmulateWideInt");
  }
  ::llvm::StringRef getName() const override { return "ArithEmulateWideInt"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<vector::VectorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ArithEmulateWideIntBase<DerivedT>)

protected:
  ::mlir::Pass::Option<unsigned> widestIntSupported{*this, "widest-int-supported", ::llvm::cl::desc("Widest integer type supported by the target"), ::llvm::cl::init(32)};
};

template <typename DerivedT>
class ArithExpandOpsPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ArithExpandOpsPassBase;

  ArithExpandOpsPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ArithExpandOpsPassBase(const ArithExpandOpsPassBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("arith-expand");
  }
  ::llvm::StringRef getArgument() const override { return "arith-expand"; }

  ::llvm::StringRef getDescription() const override { return "Legalize Arith ops to be convertible to LLVM."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ArithExpandOpsPass");
  }
  ::llvm::StringRef getName() const override { return "ArithExpandOpsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<vector::VectorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ArithExpandOpsPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> includeBf16{*this, "include-bf16", ::llvm::cl::desc("Enable the BF16 expansion patterns"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class ArithIntNarrowingBase : public ::mlir::OperationPass<> {
public:
  using Base = ArithIntNarrowingBase;

  ArithIntNarrowingBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ArithIntNarrowingBase(const ArithIntNarrowingBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("arith-int-narrowing");
  }
  ::llvm::StringRef getArgument() const override { return "arith-int-narrowing"; }

  ::llvm::StringRef getDescription() const override { return "Reduce integer operation bitwidth"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ArithIntNarrowing");
  }
  ::llvm::StringRef getName() const override { return "ArithIntNarrowing"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<vector::VectorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ArithIntNarrowingBase<DerivedT>)

protected:
  ::mlir::Pass::ListOption<unsigned> bitwidthsSupported{*this, "int-bitwidths-supported", ::llvm::cl::desc("Integer bitwidths supported")};
};

template <typename DerivedT>
class ArithIntRangeOptsBase : public ::mlir::OperationPass<> {
public:
  using Base = ArithIntRangeOptsBase;

  ArithIntRangeOptsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ArithIntRangeOptsBase(const ArithIntRangeOptsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("int-range-optimizations");
  }
  ::llvm::StringRef getArgument() const override { return "int-range-optimizations"; }

  ::llvm::StringRef getDescription() const override { return "Do optimizations based on integer range analysis"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ArithIntRangeOpts");
  }
  ::llvm::StringRef getName() const override { return "ArithIntRangeOpts"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ArithIntRangeOptsBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ArithUnsignedWhenEquivalentBase : public ::mlir::OperationPass<> {
public:
  using Base = ArithUnsignedWhenEquivalentBase;

  ArithUnsignedWhenEquivalentBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ArithUnsignedWhenEquivalentBase(const ArithUnsignedWhenEquivalentBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("arith-unsigned-when-equivalent");
  }
  ::llvm::StringRef getArgument() const override { return "arith-unsigned-when-equivalent"; }

  ::llvm::StringRef getDescription() const override { return "Replace signed ops with unsigned ones where they are proven equivalent"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ArithUnsignedWhenEquivalent");
  }
  ::llvm::StringRef getName() const override { return "ArithUnsignedWhenEquivalent"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ArithUnsignedWhenEquivalentBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES

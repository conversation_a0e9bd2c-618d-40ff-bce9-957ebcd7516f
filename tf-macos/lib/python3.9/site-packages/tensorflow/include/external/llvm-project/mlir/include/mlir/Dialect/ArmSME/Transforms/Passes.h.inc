/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_ENABLEARMSTREAMING
#define GEN_PASS_DECL_OUTERPRODUCTFUSION
#define GEN_PASS_DECL_TILEALLOCATION
#define GEN_PASS_DECL_VECTORLEGALIZATION
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// EnableArmStreaming
//===----------------------------------------------------------------------===//
#ifdef GEN_PA<PERSON>_<PERSON><PERSON>_ENABLEARMSTREAMING
struct EnableArmStreamingOptions {
  mlir::arm_sme::ArmStreamingMode streamingMode = mlir::arm_sme::ArmStreamingMode::Streaming;
  mlir::arm_sme::ArmZaMode zaMode = mlir::arm_sme::ArmZaMode::Disabled;
  bool onlyIfRequiredByOps = false;
};
#undef GEN_PASS_DECL_ENABLEARMSTREAMING
#endif // GEN_PASS_DECL_ENABLEARMSTREAMING
#ifdef GEN_PASS_DEF_ENABLEARMSTREAMING
namespace impl {

template <typename DerivedT>
class EnableArmStreamingBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = EnableArmStreamingBase;

  EnableArmStreamingBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  EnableArmStreamingBase(const EnableArmStreamingBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("enable-arm-streaming");
  }
  ::llvm::StringRef getArgument() const override { return "enable-arm-streaming"; }

  ::llvm::StringRef getDescription() const override { return "Enable Armv9 Streaming SVE mode"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("EnableArmStreaming");
  }
  ::llvm::StringRef getName() const override { return "EnableArmStreaming"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<func::FuncDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(EnableArmStreamingBase<DerivedT>)

  EnableArmStreamingBase(const EnableArmStreamingOptions &options) : EnableArmStreamingBase() {
    streamingMode = options.streamingMode;
    zaMode = options.zaMode;
    onlyIfRequiredByOps = options.onlyIfRequiredByOps;
  }
protected:
  ::mlir::Pass::Option<mlir::arm_sme::ArmStreamingMode> streamingMode{*this, "streaming-mode", ::llvm::cl::desc("Select how streaming-mode is managed at the function-level."), ::llvm::cl::init(mlir::arm_sme::ArmStreamingMode::Streaming), ::llvm::cl::values(
                clEnumValN(mlir::arm_sme::ArmStreamingMode::Disabled,
                           "disabled", "Streaming mode is disabled."),
                clEnumValN(mlir::arm_sme::ArmStreamingMode::Streaming,
                           "streaming",
                           "Streaming mode is part of the function interface "
                           "(ABI), caller manages PSTATE.SM on entry/exit."),
                clEnumValN(mlir::arm_sme::ArmStreamingMode::StreamingLocally,
                           "streaming-locally",
                           "Streaming mode is internal to the function, callee "
                           "manages PSTATE.SM on entry/exit."),
                clEnumValN(mlir::arm_sme::ArmStreamingMode::StreamingCompatible,
                           "streaming-compatible",
                           "Function supports both streaming and non-streaming "
                           "modes.")
          )};
  ::mlir::Pass::Option<mlir::arm_sme::ArmZaMode> zaMode{*this, "za-mode", ::llvm::cl::desc("Select how ZA-storage is managed at the function-level."), ::llvm::cl::init(mlir::arm_sme::ArmZaMode::Disabled), ::llvm::cl::values(
                 clEnumValN(mlir::arm_sme::ArmZaMode::Disabled,
                            "disabled", "ZA storage is disabled."),
                 clEnumValN(mlir::arm_sme::ArmZaMode::NewZA,
                            "new-za",
                            "The function has ZA state. The ZA state is "
                            "created on entry and destroyed on exit."),
                 clEnumValN(mlir::arm_sme::ArmZaMode::InZA,
                            "in-za",
                            "The function uses ZA state. The ZA state may "
                            "be used for input."),
                 clEnumValN(mlir::arm_sme::ArmZaMode::OutZA,
                            "out-za",
                            "The function uses ZA state. The ZA state may "
                            "be used for output."),
                 clEnumValN(mlir::arm_sme::ArmZaMode::InOutZA,
                            "inout-za",
                            "The function uses ZA state. The ZA state may "
                            "be used for input and/or output."),
                 clEnumValN(mlir::arm_sme::ArmZaMode::PreservesZA,
                            "preserves-za",
                            "The function shares ZA state. The ZA state may "
                            "not be used for input and/or output and the "
                            "function must return with ZA unchanged")
           )};
  ::mlir::Pass::Option<bool> onlyIfRequiredByOps{*this, "only-if-required-by-ops", ::llvm::cl::desc("Only apply the selected streaming/ZA modes if the function  contains ops that require them."), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_ENABLEARMSTREAMING
#endif // GEN_PASS_DEF_ENABLEARMSTREAMING

//===----------------------------------------------------------------------===//
// OuterProductFusion
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_OUTERPRODUCTFUSION
#undef GEN_PASS_DECL_OUTERPRODUCTFUSION
#endif // GEN_PASS_DECL_OUTERPRODUCTFUSION
#ifdef GEN_PASS_DEF_OUTERPRODUCTFUSION
namespace impl {

template <typename DerivedT>
class OuterProductFusionBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = OuterProductFusionBase;

  OuterProductFusionBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  OuterProductFusionBase(const OuterProductFusionBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("arm-sme-outer-product-fusion");
  }
  ::llvm::StringRef getArgument() const override { return "arm-sme-outer-product-fusion"; }

  ::llvm::StringRef getDescription() const override { return "Fuse 'arm_sme.outerproduct' operations into 2-way or 4-way widening variants"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("OuterProductFusion");
  }
  ::llvm::StringRef getName() const override { return "OuterProductFusion"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<func::FuncDialect>();
    registry.insert<arm_sme::ArmSMEDialect>();
    registry.insert<LLVM::LLVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(OuterProductFusionBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_OUTERPRODUCTFUSION
#endif // GEN_PASS_DEF_OUTERPRODUCTFUSION

//===----------------------------------------------------------------------===//
// TileAllocation
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TILEALLOCATION
#undef GEN_PASS_DECL_TILEALLOCATION
#endif // GEN_PASS_DECL_TILEALLOCATION
#ifdef GEN_PASS_DEF_TILEALLOCATION
namespace impl {

template <typename DerivedT>
class TileAllocationBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = TileAllocationBase;

  TileAllocationBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  TileAllocationBase(const TileAllocationBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("allocate-arm-sme-tiles");
  }
  ::llvm::StringRef getArgument() const override { return "allocate-arm-sme-tiles"; }

  ::llvm::StringRef getDescription() const override { return "Allocate SME tiles"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TileAllocation");
  }
  ::llvm::StringRef getName() const override { return "TileAllocation"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<func::FuncDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TileAllocationBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TILEALLOCATION
#endif // GEN_PASS_DEF_TILEALLOCATION

//===----------------------------------------------------------------------===//
// VectorLegalization
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_VECTORLEGALIZATION
#undef GEN_PASS_DECL_VECTORLEGALIZATION
#endif // GEN_PASS_DECL_VECTORLEGALIZATION
#ifdef GEN_PASS_DEF_VECTORLEGALIZATION
namespace impl {

template <typename DerivedT>
class VectorLegalizationBase : public ::mlir::OperationPass<mlir::ModuleOp> {
public:
  using Base = VectorLegalizationBase;

  VectorLegalizationBase() : ::mlir::OperationPass<mlir::ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  VectorLegalizationBase(const VectorLegalizationBase &other) : ::mlir::OperationPass<mlir::ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("arm-sme-vector-legalization");
  }
  ::llvm::StringRef getArgument() const override { return "arm-sme-vector-legalization"; }

  ::llvm::StringRef getDescription() const override { return "Legalize vectors for ArmSME"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("VectorLegalization");
  }
  ::llvm::StringRef getName() const override { return "VectorLegalization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<func::FuncDialect>();
    registry.insert<arm_sme::ArmSMEDialect>();
    registry.insert<vector::VectorDialect>();
    registry.insert<arith::ArithDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(VectorLegalizationBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_VECTORLEGALIZATION
#endif // GEN_PASS_DEF_VECTORLEGALIZATION
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// EnableArmStreaming Registration
//===----------------------------------------------------------------------===//

inline void registerEnableArmStreaming() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::arm_sme::createEnableArmStreamingPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerEnableArmStreamingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::arm_sme::createEnableArmStreamingPass();
  });
}

//===----------------------------------------------------------------------===//
// OuterProductFusion Registration
//===----------------------------------------------------------------------===//

inline void registerOuterProductFusion() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::arm_sme::createOuterProductFusionPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerOuterProductFusionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::arm_sme::createOuterProductFusionPass();
  });
}

//===----------------------------------------------------------------------===//
// TileAllocation Registration
//===----------------------------------------------------------------------===//

inline void registerTileAllocation() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::arm_sme::createTileAllocationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTileAllocationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::arm_sme::createTileAllocationPass();
  });
}

//===----------------------------------------------------------------------===//
// VectorLegalization Registration
//===----------------------------------------------------------------------===//

inline void registerVectorLegalization() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::arm_sme::createVectorLegalizationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerVectorLegalizationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::arm_sme::createVectorLegalizationPass();
  });
}

//===----------------------------------------------------------------------===//
// ArmSME Registration
//===----------------------------------------------------------------------===//

inline void registerArmSMEPasses() {
  registerEnableArmStreaming();
  registerOuterProductFusion();
  registerTileAllocation();
  registerVectorLegalization();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class EnableArmStreamingBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = EnableArmStreamingBase;

  EnableArmStreamingBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  EnableArmStreamingBase(const EnableArmStreamingBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("enable-arm-streaming");
  }
  ::llvm::StringRef getArgument() const override { return "enable-arm-streaming"; }

  ::llvm::StringRef getDescription() const override { return "Enable Armv9 Streaming SVE mode"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("EnableArmStreaming");
  }
  ::llvm::StringRef getName() const override { return "EnableArmStreaming"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<func::FuncDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(EnableArmStreamingBase<DerivedT>)

protected:
  ::mlir::Pass::Option<mlir::arm_sme::ArmStreamingMode> streamingMode{*this, "streaming-mode", ::llvm::cl::desc("Select how streaming-mode is managed at the function-level."), ::llvm::cl::init(mlir::arm_sme::ArmStreamingMode::Streaming), ::llvm::cl::values(
                clEnumValN(mlir::arm_sme::ArmStreamingMode::Disabled,
                           "disabled", "Streaming mode is disabled."),
                clEnumValN(mlir::arm_sme::ArmStreamingMode::Streaming,
                           "streaming",
                           "Streaming mode is part of the function interface "
                           "(ABI), caller manages PSTATE.SM on entry/exit."),
                clEnumValN(mlir::arm_sme::ArmStreamingMode::StreamingLocally,
                           "streaming-locally",
                           "Streaming mode is internal to the function, callee "
                           "manages PSTATE.SM on entry/exit."),
                clEnumValN(mlir::arm_sme::ArmStreamingMode::StreamingCompatible,
                           "streaming-compatible",
                           "Function supports both streaming and non-streaming "
                           "modes.")
          )};
  ::mlir::Pass::Option<mlir::arm_sme::ArmZaMode> zaMode{*this, "za-mode", ::llvm::cl::desc("Select how ZA-storage is managed at the function-level."), ::llvm::cl::init(mlir::arm_sme::ArmZaMode::Disabled), ::llvm::cl::values(
                 clEnumValN(mlir::arm_sme::ArmZaMode::Disabled,
                            "disabled", "ZA storage is disabled."),
                 clEnumValN(mlir::arm_sme::ArmZaMode::NewZA,
                            "new-za",
                            "The function has ZA state. The ZA state is "
                            "created on entry and destroyed on exit."),
                 clEnumValN(mlir::arm_sme::ArmZaMode::InZA,
                            "in-za",
                            "The function uses ZA state. The ZA state may "
                            "be used for input."),
                 clEnumValN(mlir::arm_sme::ArmZaMode::OutZA,
                            "out-za",
                            "The function uses ZA state. The ZA state may "
                            "be used for output."),
                 clEnumValN(mlir::arm_sme::ArmZaMode::InOutZA,
                            "inout-za",
                            "The function uses ZA state. The ZA state may "
                            "be used for input and/or output."),
                 clEnumValN(mlir::arm_sme::ArmZaMode::PreservesZA,
                            "preserves-za",
                            "The function shares ZA state. The ZA state may "
                            "not be used for input and/or output and the "
                            "function must return with ZA unchanged")
           )};
  ::mlir::Pass::Option<bool> onlyIfRequiredByOps{*this, "only-if-required-by-ops", ::llvm::cl::desc("Only apply the selected streaming/ZA modes if the function  contains ops that require them."), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class OuterProductFusionBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = OuterProductFusionBase;

  OuterProductFusionBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  OuterProductFusionBase(const OuterProductFusionBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("arm-sme-outer-product-fusion");
  }
  ::llvm::StringRef getArgument() const override { return "arm-sme-outer-product-fusion"; }

  ::llvm::StringRef getDescription() const override { return "Fuse 'arm_sme.outerproduct' operations into 2-way or 4-way widening variants"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("OuterProductFusion");
  }
  ::llvm::StringRef getName() const override { return "OuterProductFusion"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<func::FuncDialect>();
    registry.insert<arm_sme::ArmSMEDialect>();
    registry.insert<LLVM::LLVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(OuterProductFusionBase<DerivedT>)

protected:
};

template <typename DerivedT>
class TileAllocationBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = TileAllocationBase;

  TileAllocationBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  TileAllocationBase(const TileAllocationBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("allocate-arm-sme-tiles");
  }
  ::llvm::StringRef getArgument() const override { return "allocate-arm-sme-tiles"; }

  ::llvm::StringRef getDescription() const override { return "Allocate SME tiles"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TileAllocation");
  }
  ::llvm::StringRef getName() const override { return "TileAllocation"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<func::FuncDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TileAllocationBase<DerivedT>)

protected:
};

template <typename DerivedT>
class VectorLegalizationBase : public ::mlir::OperationPass<mlir::ModuleOp> {
public:
  using Base = VectorLegalizationBase;

  VectorLegalizationBase() : ::mlir::OperationPass<mlir::ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  VectorLegalizationBase(const VectorLegalizationBase &other) : ::mlir::OperationPass<mlir::ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("arm-sme-vector-legalization");
  }
  ::llvm::StringRef getArgument() const override { return "arm-sme-vector-legalization"; }

  ::llvm::StringRef getDescription() const override { return "Legalize vectors for ArmSME"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("VectorLegalization");
  }
  ::llvm::StringRef getName() const override { return "VectorLegalization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<func::FuncDialect>();
    registry.insert<arm_sme::ArmSMEDialect>();
    registry.insert<vector::VectorDialect>();
    registry.insert<arith::ArithDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(VectorLegalizationBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES

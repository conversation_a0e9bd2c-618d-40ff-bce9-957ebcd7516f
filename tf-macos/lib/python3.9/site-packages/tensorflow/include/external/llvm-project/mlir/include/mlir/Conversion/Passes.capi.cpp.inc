/* Autogenerated by mlir-tblgen; don't manually edit. */
//===----------------------------------------------------------------------===//
// Conversion Group Registration
//===----------------------------------------------------------------------===//

void mlirRegisterConversionPasses(void) {
  registerConversionPasses();
}

MlirPass mlirCreateConversionArithToAMDGPUConversionPass(void) {
  return wrap(createArithToAMDGPUConversionPass().release());
}
void mlirRegisterConversionArithToAMDGPUConversionPass(void) {
  registerArithToAMDGPUConversionPass();
}


MlirPass mlirCreateConversionArithToArmSMEConversionPass(void) {
  return wrap(createArithToArmSMEConversionPass().release());
}
void mlirRegisterConversionArithToArmSMEConversionPass(void) {
  registerArithToArmSMEConversionPass();
}


MlirPass mlirCreateConversionArithToLLVMConversionPass(void) {
  return wrap(createArithToLLVMConversionPass().release());
}
void mlirRegisterConversionArithToLLVMConversionPass(void) {
  registerArithToLLVMConversionPass();
}


MlirPass mlirCreateConversionConvertAMDGPUToROCDL(void) {
  return wrap(mlir::createConvertAMDGPUToROCDLPass().release());
}
void mlirRegisterConversionConvertAMDGPUToROCDL(void) {
  registerConvertAMDGPUToROCDL();
}


MlirPass mlirCreateConversionConvertAffineForToGPU(void) {
  return wrap(mlir::createAffineForToGPUPass().release());
}
void mlirRegisterConversionConvertAffineForToGPU(void) {
  registerConvertAffineForToGPU();
}


MlirPass mlirCreateConversionConvertAffineToStandard(void) {
  return wrap(mlir::createLowerAffinePass().release());
}
void mlirRegisterConversionConvertAffineToStandard(void) {
  registerConvertAffineToStandard();
}


MlirPass mlirCreateConversionConvertArithToSPIRV(void) {
  return wrap(mlir::arith::createConvertArithToSPIRVPass().release());
}
void mlirRegisterConversionConvertArithToSPIRV(void) {
  registerConvertArithToSPIRV();
}


MlirPass mlirCreateConversionConvertArmNeon2dToIntr(void) {
  return wrap(mlir::createConvertArmNeon2dToIntrPass().release());
}
void mlirRegisterConversionConvertArmNeon2dToIntr(void) {
  registerConvertArmNeon2dToIntr();
}


MlirPass mlirCreateConversionConvertArmSMEToLLVM(void) {
  return wrap(mlir::createConvertArmSMEToLLVMPass().release());
}
void mlirRegisterConversionConvertArmSMEToLLVM(void) {
  registerConvertArmSMEToLLVM();
}


MlirPass mlirCreateConversionConvertArmSMEToSCF(void) {
  return wrap(mlir::createConvertArmSMEToSCFPass().release());
}
void mlirRegisterConversionConvertArmSMEToSCF(void) {
  registerConvertArmSMEToSCF();
}


MlirPass mlirCreateConversionConvertAsyncToLLVMPass(void) {
  return wrap(createConvertAsyncToLLVMPass().release());
}
void mlirRegisterConversionConvertAsyncToLLVMPass(void) {
  registerConvertAsyncToLLVMPass();
}


MlirPass mlirCreateConversionConvertBufferizationToMemRef(void) {
  return wrap(mlir::createBufferizationToMemRefPass().release());
}
void mlirRegisterConversionConvertBufferizationToMemRef(void) {
  registerConvertBufferizationToMemRef();
}


MlirPass mlirCreateConversionConvertComplexToLLVMPass(void) {
  return wrap(createConvertComplexToLLVMPass().release());
}
void mlirRegisterConversionConvertComplexToLLVMPass(void) {
  registerConvertComplexToLLVMPass();
}


MlirPass mlirCreateConversionConvertComplexToLibm(void) {
  return wrap(mlir::createConvertComplexToLibmPass().release());
}
void mlirRegisterConversionConvertComplexToLibm(void) {
  registerConvertComplexToLibm();
}


MlirPass mlirCreateConversionConvertComplexToSPIRVPass(void) {
  return wrap(createConvertComplexToSPIRVPass().release());
}
void mlirRegisterConversionConvertComplexToSPIRVPass(void) {
  registerConvertComplexToSPIRVPass();
}


MlirPass mlirCreateConversionConvertComplexToStandard(void) {
  return wrap(mlir::createConvertComplexToStandardPass().release());
}
void mlirRegisterConversionConvertComplexToStandard(void) {
  registerConvertComplexToStandard();
}


MlirPass mlirCreateConversionConvertControlFlowToLLVMPass(void) {
  return wrap(createConvertControlFlowToLLVMPass().release());
}
void mlirRegisterConversionConvertControlFlowToLLVMPass(void) {
  registerConvertControlFlowToLLVMPass();
}


MlirPass mlirCreateConversionConvertControlFlowToSPIRV(void) {
  return wrap(mlir::createConvertControlFlowToSPIRVPass().release());
}
void mlirRegisterConversionConvertControlFlowToSPIRV(void) {
  registerConvertControlFlowToSPIRV();
}


MlirPass mlirCreateConversionConvertFuncToEmitC(void) {
  return wrap(createConvertFuncToEmitC().release());
}
void mlirRegisterConversionConvertFuncToEmitC(void) {
  registerConvertFuncToEmitC();
}


MlirPass mlirCreateConversionConvertFuncToLLVMPass(void) {
  return wrap(createConvertFuncToLLVMPass().release());
}
void mlirRegisterConversionConvertFuncToLLVMPass(void) {
  registerConvertFuncToLLVMPass();
}


MlirPass mlirCreateConversionConvertFuncToSPIRV(void) {
  return wrap(mlir::createConvertFuncToSPIRVPass().release());
}
void mlirRegisterConversionConvertFuncToSPIRV(void) {
  registerConvertFuncToSPIRV();
}


MlirPass mlirCreateConversionConvertGPUToSPIRV(void) {
  return wrap(mlir::createConvertGPUToSPIRVPass().release());
}
void mlirRegisterConversionConvertGPUToSPIRV(void) {
  registerConvertGPUToSPIRV();
}


MlirPass mlirCreateConversionConvertGpuLaunchFuncToVulkanLaunchFunc(void) {
  return wrap(mlir::createConvertGpuLaunchFuncToVulkanLaunchFuncPass().release());
}
void mlirRegisterConversionConvertGpuLaunchFuncToVulkanLaunchFunc(void) {
  registerConvertGpuLaunchFuncToVulkanLaunchFunc();
}


MlirPass mlirCreateConversionConvertGpuOpsToNVVMOps(void) {
  return wrap(createConvertGpuOpsToNVVMOps().release());
}
void mlirRegisterConversionConvertGpuOpsToNVVMOps(void) {
  registerConvertGpuOpsToNVVMOps();
}


MlirPass mlirCreateConversionConvertGpuOpsToROCDLOps(void) {
  return wrap(mlir::createLowerGpuOpsToROCDLOpsPass().release());
}
void mlirRegisterConversionConvertGpuOpsToROCDLOps(void) {
  registerConvertGpuOpsToROCDLOps();
}


MlirPass mlirCreateConversionConvertIndexToLLVMPass(void) {
  return wrap(createConvertIndexToLLVMPass().release());
}
void mlirRegisterConversionConvertIndexToLLVMPass(void) {
  registerConvertIndexToLLVMPass();
}


MlirPass mlirCreateConversionConvertIndexToSPIRVPass(void) {
  return wrap(createConvertIndexToSPIRVPass().release());
}
void mlirRegisterConversionConvertIndexToSPIRVPass(void) {
  registerConvertIndexToSPIRVPass();
}


MlirPass mlirCreateConversionConvertLinalgToStandard(void) {
  return wrap(mlir::createConvertLinalgToStandardPass().release());
}
void mlirRegisterConversionConvertLinalgToStandard(void) {
  registerConvertLinalgToStandard();
}


MlirPass mlirCreateConversionConvertMathToFuncs(void) {
  return wrap(createConvertMathToFuncs().release());
}
void mlirRegisterConversionConvertMathToFuncs(void) {
  registerConvertMathToFuncs();
}


MlirPass mlirCreateConversionConvertMathToLLVMPass(void) {
  return wrap(createConvertMathToLLVMPass().release());
}
void mlirRegisterConversionConvertMathToLLVMPass(void) {
  registerConvertMathToLLVMPass();
}


MlirPass mlirCreateConversionConvertMathToLibm(void) {
  return wrap(mlir::createConvertMathToLibmPass().release());
}
void mlirRegisterConversionConvertMathToLibm(void) {
  registerConvertMathToLibm();
}


MlirPass mlirCreateConversionConvertMathToSPIRV(void) {
  return wrap(mlir::createConvertMathToSPIRVPass().release());
}
void mlirRegisterConversionConvertMathToSPIRV(void) {
  registerConvertMathToSPIRV();
}


MlirPass mlirCreateConversionConvertMemRefToSPIRV(void) {
  return wrap(mlir::createConvertMemRefToSPIRVPass().release());
}
void mlirRegisterConversionConvertMemRefToSPIRV(void) {
  registerConvertMemRefToSPIRV();
}


MlirPass mlirCreateConversionConvertNVGPUToNVVMPass(void) {
  return wrap(createConvertNVGPUToNVVMPass().release());
}
void mlirRegisterConversionConvertNVGPUToNVVMPass(void) {
  registerConvertNVGPUToNVVMPass();
}


MlirPass mlirCreateConversionConvertNVVMToLLVMPass(void) {
  return wrap(createConvertNVVMToLLVMPass().release());
}
void mlirRegisterConversionConvertNVVMToLLVMPass(void) {
  registerConvertNVVMToLLVMPass();
}


MlirPass mlirCreateConversionConvertOpenACCToSCF(void) {
  return wrap(mlir::createConvertOpenACCToSCFPass().release());
}
void mlirRegisterConversionConvertOpenACCToSCF(void) {
  registerConvertOpenACCToSCF();
}


MlirPass mlirCreateConversionConvertOpenMPToLLVMPass(void) {
  return wrap(createConvertOpenMPToLLVMPass().release());
}
void mlirRegisterConversionConvertOpenMPToLLVMPass(void) {
  registerConvertOpenMPToLLVMPass();
}


MlirPass mlirCreateConversionConvertPDLToPDLInterp(void) {
  return wrap(mlir::createPDLToPDLInterpPass().release());
}
void mlirRegisterConversionConvertPDLToPDLInterp(void) {
  registerConvertPDLToPDLInterp();
}


MlirPass mlirCreateConversionConvertParallelLoopToGpu(void) {
  return wrap(mlir::createParallelLoopToGpuPass().release());
}
void mlirRegisterConversionConvertParallelLoopToGpu(void) {
  registerConvertParallelLoopToGpu();
}


MlirPass mlirCreateConversionConvertSCFToOpenMPPass(void) {
  return wrap(createConvertSCFToOpenMPPass().release());
}
void mlirRegisterConversionConvertSCFToOpenMPPass(void) {
  registerConvertSCFToOpenMPPass();
}


MlirPass mlirCreateConversionConvertSPIRVToLLVMPass(void) {
  return wrap(createConvertSPIRVToLLVMPass().release());
}
void mlirRegisterConversionConvertSPIRVToLLVMPass(void) {
  registerConvertSPIRVToLLVMPass();
}


MlirPass mlirCreateConversionConvertShapeConstraints(void) {
  return wrap(mlir::createConvertShapeConstraintsPass().release());
}
void mlirRegisterConversionConvertShapeConstraints(void) {
  registerConvertShapeConstraints();
}


MlirPass mlirCreateConversionConvertShapeToStandard(void) {
  return wrap(mlir::createConvertShapeToStandardPass().release());
}
void mlirRegisterConversionConvertShapeToStandard(void) {
  registerConvertShapeToStandard();
}


MlirPass mlirCreateConversionConvertTensorToLinalg(void) {
  return wrap(mlir::createConvertTensorToLinalgPass().release());
}
void mlirRegisterConversionConvertTensorToLinalg(void) {
  registerConvertTensorToLinalg();
}


MlirPass mlirCreateConversionConvertTensorToSPIRV(void) {
  return wrap(mlir::createConvertTensorToSPIRVPass().release());
}
void mlirRegisterConversionConvertTensorToSPIRV(void) {
  registerConvertTensorToSPIRV();
}


MlirPass mlirCreateConversionConvertToLLVMPass(void) {
  return wrap(mlir::createConvertToLLVMPass().release());
}
void mlirRegisterConversionConvertToLLVMPass(void) {
  registerConvertToLLVMPass();
}


MlirPass mlirCreateConversionConvertVectorToArmSME(void) {
  return wrap(mlir::createConvertVectorToArmSMEPass().release());
}
void mlirRegisterConversionConvertVectorToArmSME(void) {
  registerConvertVectorToArmSME();
}


MlirPass mlirCreateConversionConvertVectorToGPU(void) {
  return wrap(mlir::createConvertVectorToGPUPass().release());
}
void mlirRegisterConversionConvertVectorToGPU(void) {
  registerConvertVectorToGPU();
}


MlirPass mlirCreateConversionConvertVectorToLLVMPass(void) {
  return wrap(createConvertVectorToLLVMPass().release());
}
void mlirRegisterConversionConvertVectorToLLVMPass(void) {
  registerConvertVectorToLLVMPass();
}


MlirPass mlirCreateConversionConvertVectorToSCF(void) {
  return wrap(mlir::createConvertVectorToSCFPass().release());
}
void mlirRegisterConversionConvertVectorToSCF(void) {
  registerConvertVectorToSCF();
}


MlirPass mlirCreateConversionConvertVectorToSPIRV(void) {
  return wrap(mlir::createConvertVectorToSPIRVPass().release());
}
void mlirRegisterConversionConvertVectorToSPIRV(void) {
  registerConvertVectorToSPIRV();
}


MlirPass mlirCreateConversionConvertVulkanLaunchFuncToVulkanCallsPass(void) {
  return wrap(createConvertVulkanLaunchFuncToVulkanCallsPass().release());
}
void mlirRegisterConversionConvertVulkanLaunchFuncToVulkanCallsPass(void) {
  registerConvertVulkanLaunchFuncToVulkanCallsPass();
}


MlirPass mlirCreateConversionFinalizeMemRefToLLVMConversionPass(void) {
  return wrap(createFinalizeMemRefToLLVMConversionPass().release());
}
void mlirRegisterConversionFinalizeMemRefToLLVMConversionPass(void) {
  registerFinalizeMemRefToLLVMConversionPass();
}


MlirPass mlirCreateConversionGpuToLLVMConversionPass(void) {
  return wrap(createGpuToLLVMConversionPass().release());
}
void mlirRegisterConversionGpuToLLVMConversionPass(void) {
  registerGpuToLLVMConversionPass();
}


MlirPass mlirCreateConversionLiftControlFlowToSCFPass(void) {
  return wrap(createLiftControlFlowToSCFPass().release());
}
void mlirRegisterConversionLiftControlFlowToSCFPass(void) {
  registerLiftControlFlowToSCFPass();
}


MlirPass mlirCreateConversionLowerHostCodeToLLVMPass(void) {
  return wrap(createLowerHostCodeToLLVMPass().release());
}
void mlirRegisterConversionLowerHostCodeToLLVMPass(void) {
  registerLowerHostCodeToLLVMPass();
}


MlirPass mlirCreateConversionMapMemRefStorageClass(void) {
  return wrap(mlir::createMapMemRefStorageClassPass().release());
}
void mlirRegisterConversionMapMemRefStorageClass(void) {
  registerMapMemRefStorageClass();
}


MlirPass mlirCreateConversionReconcileUnrealizedCasts(void) {
  return wrap(mlir::createReconcileUnrealizedCastsPass().release());
}
void mlirRegisterConversionReconcileUnrealizedCasts(void) {
  registerReconcileUnrealizedCasts();
}


MlirPass mlirCreateConversionSCFToControlFlow(void) {
  return wrap(mlir::createConvertSCFToCFPass().release());
}
void mlirRegisterConversionSCFToControlFlow(void) {
  registerSCFToControlFlow();
}


MlirPass mlirCreateConversionSCFToEmitC(void) {
  return wrap(createSCFToEmitC().release());
}
void mlirRegisterConversionSCFToEmitC(void) {
  registerSCFToEmitC();
}


MlirPass mlirCreateConversionSCFToSPIRV(void) {
  return wrap(createSCFToSPIRV().release());
}
void mlirRegisterConversionSCFToSPIRV(void) {
  registerSCFToSPIRV();
}


MlirPass mlirCreateConversionSetLLVMModuleDataLayoutPass(void) {
  return wrap(createSetLLVMModuleDataLayoutPass().release());
}
void mlirRegisterConversionSetLLVMModuleDataLayoutPass(void) {
  registerSetLLVMModuleDataLayoutPass();
}


MlirPass mlirCreateConversionTosaToArith(void) {
  return wrap(tosa::createTosaToArith().release());
}
void mlirRegisterConversionTosaToArith(void) {
  registerTosaToArith();
}


MlirPass mlirCreateConversionTosaToLinalg(void) {
  return wrap(tosa::createTosaToLinalg().release());
}
void mlirRegisterConversionTosaToLinalg(void) {
  registerTosaToLinalg();
}


MlirPass mlirCreateConversionTosaToLinalgNamed(void) {
  return wrap(tosa::createTosaToLinalgNamed().release());
}
void mlirRegisterConversionTosaToLinalgNamed(void) {
  registerTosaToLinalgNamed();
}


MlirPass mlirCreateConversionTosaToMLProgram(void) {
  return wrap(createTosaToMLProgram().release());
}
void mlirRegisterConversionTosaToMLProgram(void) {
  registerTosaToMLProgram();
}


MlirPass mlirCreateConversionTosaToSCF(void) {
  return wrap(tosa::createTosaToSCF().release());
}
void mlirRegisterConversionTosaToSCF(void) {
  registerTosaToSCF();
}


MlirPass mlirCreateConversionTosaToTensor(void) {
  return wrap(tosa::createTosaToTensor().release());
}
void mlirRegisterConversionTosaToTensor(void) {
  registerTosaToTensor();
}


MlirPass mlirCreateConversionUBToLLVMConversionPass(void) {
  return wrap(createUBToLLVMConversionPass().release());
}
void mlirRegisterConversionUBToLLVMConversionPass(void) {
  registerUBToLLVMConversionPass();
}


MlirPass mlirCreateConversionUBToSPIRVConversionPass(void) {
  return wrap(createUBToSPIRVConversionPass().release());
}
void mlirRegisterConversionUBToSPIRVConversionPass(void) {
  registerUBToSPIRVConversionPass();
}


/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Op Declarations                                                            *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|* From: ArmSMEIntrinsicOps.td                                                *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

#if defined(GET_OP_CLASSES) || defined(GET_OP_FWD_DEFINES)
#undef GET_OP_FWD_DEFINES
namespace mlir {
namespace arm_sme {
class aarch64_sme_cntsb;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_cntsd;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_cntsh;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_cntsw;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_ld1b_horiz;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_ld1b_vert;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_ld1d_horiz;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_ld1d_vert;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_ld1h_horiz;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_ld1h_vert;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_ld1q_horiz;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_ld1q_vert;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_ld1w_horiz;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_ld1w_vert;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_mopa;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_mopa_wide;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_mops;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_mops_wide;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_read_horiz;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_read_vert;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_smopa_wide;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_smopa_za32;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_smops_wide;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_smops_za32;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_st1b_horiz;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_st1b_vert;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_st1d_horiz;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_st1d_vert;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_st1h_horiz;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_st1h_vert;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_st1q_horiz;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_st1q_vert;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_st1w_horiz;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_st1w_vert;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_str;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_sumopa_wide;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_sumops_wide;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_umopa_wide;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_umopa_za32;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_umops_wide;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_umops_za32;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_usmopa_wide;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_usmops_wide;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_write_horiz;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_write_vert;
} // namespace arm_sme
} // namespace mlir
namespace mlir {
namespace arm_sme {
class aarch64_sme_zero;
} // namespace arm_sme
} // namespace mlir
#endif

#ifdef GET_OP_CLASSES
#undef GET_OP_CLASSES


//===----------------------------------------------------------------------===//
// Local Utility Method Definitions
//===----------------------------------------------------------------------===//

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_cntsb declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_cntsbGenericAdaptorBase {
public:
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_cntsbGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const ::mlir::EmptyProperties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_cntsbGenericAdaptorBase(aarch64_sme_cntsb op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  ::mlir::DictionaryAttr getAttributes();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_cntsbGenericAdaptor : public detail::aarch64_sme_cntsbGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_cntsbGenericAdaptorBase;
public:
  aarch64_sme_cntsbGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const ::mlir::EmptyProperties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_cntsbGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_cntsbGenericAdaptor(values, attrs, (properties ? *properties.as<::mlir::EmptyProperties *>() : ::mlir::EmptyProperties{}), regions) {}

  template <typename LateInst = aarch64_sme_cntsb, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_cntsb>>>
  aarch64_sme_cntsbGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_cntsbAdaptor : public aarch64_sme_cntsbGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_cntsbGenericAdaptor::aarch64_sme_cntsbGenericAdaptor;
  aarch64_sme_cntsbAdaptor(aarch64_sme_cntsb op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_cntsb : public ::mlir::Op<aarch64_sme_cntsb, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::OneResult, ::mlir::OpTrait::OneTypedResult<::mlir::Type>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::ZeroOperands, ::mlir::OpTrait::OpInvariants> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_cntsbAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_cntsbGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    return {};
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.cntsb");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  ::mlir::Value getRes();
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type res);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_cntsb)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_cntsd declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_cntsdGenericAdaptorBase {
public:
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_cntsdGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const ::mlir::EmptyProperties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_cntsdGenericAdaptorBase(aarch64_sme_cntsd op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  ::mlir::DictionaryAttr getAttributes();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_cntsdGenericAdaptor : public detail::aarch64_sme_cntsdGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_cntsdGenericAdaptorBase;
public:
  aarch64_sme_cntsdGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const ::mlir::EmptyProperties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_cntsdGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_cntsdGenericAdaptor(values, attrs, (properties ? *properties.as<::mlir::EmptyProperties *>() : ::mlir::EmptyProperties{}), regions) {}

  template <typename LateInst = aarch64_sme_cntsd, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_cntsd>>>
  aarch64_sme_cntsdGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_cntsdAdaptor : public aarch64_sme_cntsdGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_cntsdGenericAdaptor::aarch64_sme_cntsdGenericAdaptor;
  aarch64_sme_cntsdAdaptor(aarch64_sme_cntsd op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_cntsd : public ::mlir::Op<aarch64_sme_cntsd, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::OneResult, ::mlir::OpTrait::OneTypedResult<::mlir::Type>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::ZeroOperands, ::mlir::OpTrait::OpInvariants> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_cntsdAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_cntsdGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    return {};
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.cntsd");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  ::mlir::Value getRes();
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type res);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_cntsd)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_cntsh declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_cntshGenericAdaptorBase {
public:
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_cntshGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const ::mlir::EmptyProperties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_cntshGenericAdaptorBase(aarch64_sme_cntsh op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  ::mlir::DictionaryAttr getAttributes();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_cntshGenericAdaptor : public detail::aarch64_sme_cntshGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_cntshGenericAdaptorBase;
public:
  aarch64_sme_cntshGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const ::mlir::EmptyProperties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_cntshGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_cntshGenericAdaptor(values, attrs, (properties ? *properties.as<::mlir::EmptyProperties *>() : ::mlir::EmptyProperties{}), regions) {}

  template <typename LateInst = aarch64_sme_cntsh, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_cntsh>>>
  aarch64_sme_cntshGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_cntshAdaptor : public aarch64_sme_cntshGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_cntshGenericAdaptor::aarch64_sme_cntshGenericAdaptor;
  aarch64_sme_cntshAdaptor(aarch64_sme_cntsh op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_cntsh : public ::mlir::Op<aarch64_sme_cntsh, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::OneResult, ::mlir::OpTrait::OneTypedResult<::mlir::Type>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::ZeroOperands, ::mlir::OpTrait::OpInvariants> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_cntshAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_cntshGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    return {};
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.cntsh");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  ::mlir::Value getRes();
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type res);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_cntsh)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_cntsw declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_cntswGenericAdaptorBase {
public:
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_cntswGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const ::mlir::EmptyProperties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_cntswGenericAdaptorBase(aarch64_sme_cntsw op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  ::mlir::DictionaryAttr getAttributes();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_cntswGenericAdaptor : public detail::aarch64_sme_cntswGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_cntswGenericAdaptorBase;
public:
  aarch64_sme_cntswGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const ::mlir::EmptyProperties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_cntswGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_cntswGenericAdaptor(values, attrs, (properties ? *properties.as<::mlir::EmptyProperties *>() : ::mlir::EmptyProperties{}), regions) {}

  template <typename LateInst = aarch64_sme_cntsw, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_cntsw>>>
  aarch64_sme_cntswGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_cntswAdaptor : public aarch64_sme_cntswGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_cntswGenericAdaptor::aarch64_sme_cntswGenericAdaptor;
  aarch64_sme_cntswAdaptor(aarch64_sme_cntsw op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_cntsw : public ::mlir::Op<aarch64_sme_cntsw, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::OneResult, ::mlir::OpTrait::OneTypedResult<::mlir::Type>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::ZeroOperands, ::mlir::OpTrait::OpInvariants> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_cntswAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_cntswGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    return {};
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.cntsw");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  ::mlir::Value getRes();
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type res);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_cntsw)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_ld1b_horiz declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_ld1b_horizGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_ld1b_horizGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_ld1b_horizGenericAdaptorBase(aarch64_sme_ld1b_horiz op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_ld1b_horizGenericAdaptor : public detail::aarch64_sme_ld1b_horizGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_ld1b_horizGenericAdaptorBase;
public:
  aarch64_sme_ld1b_horizGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_ld1b_horizGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_ld1b_horizGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_ld1b_horiz, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_ld1b_horiz>>>
  aarch64_sme_ld1b_horizGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getLoadAddress() {
    return (*getODSOperands(1).begin());
  }

  ValueT getTileSliceIndex() {
    return (*getODSOperands(2).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_ld1b_horizAdaptor : public aarch64_sme_ld1b_horizGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_ld1b_horizGenericAdaptor::aarch64_sme_ld1b_horizGenericAdaptor;
  aarch64_sme_ld1b_horizAdaptor(aarch64_sme_ld1b_horiz op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_ld1b_horiz : public ::mlir::Op<aarch64_sme_ld1b_horiz, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<3>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_ld1b_horizAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_ld1b_horizGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.ld1b.horiz");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getPredicate();
  ::mlir::TypedValue<::mlir::LLVM::LLVMPointerType> getLoadAddress();
  ::mlir::TypedValue<::mlir::IntegerType> getTileSliceIndex();
  ::mlir::OpOperand &getPredicateMutable();
  ::mlir::OpOperand &getLoadAddressMutable();
  ::mlir::OpOperand &getTileSliceIndexMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value load_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value load_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value load_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value load_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_ld1b_horiz)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_ld1b_vert declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_ld1b_vertGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_ld1b_vertGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_ld1b_vertGenericAdaptorBase(aarch64_sme_ld1b_vert op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_ld1b_vertGenericAdaptor : public detail::aarch64_sme_ld1b_vertGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_ld1b_vertGenericAdaptorBase;
public:
  aarch64_sme_ld1b_vertGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_ld1b_vertGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_ld1b_vertGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_ld1b_vert, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_ld1b_vert>>>
  aarch64_sme_ld1b_vertGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getLoadAddress() {
    return (*getODSOperands(1).begin());
  }

  ValueT getTileSliceIndex() {
    return (*getODSOperands(2).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_ld1b_vertAdaptor : public aarch64_sme_ld1b_vertGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_ld1b_vertGenericAdaptor::aarch64_sme_ld1b_vertGenericAdaptor;
  aarch64_sme_ld1b_vertAdaptor(aarch64_sme_ld1b_vert op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_ld1b_vert : public ::mlir::Op<aarch64_sme_ld1b_vert, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<3>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_ld1b_vertAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_ld1b_vertGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.ld1b.vert");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getPredicate();
  ::mlir::TypedValue<::mlir::LLVM::LLVMPointerType> getLoadAddress();
  ::mlir::TypedValue<::mlir::IntegerType> getTileSliceIndex();
  ::mlir::OpOperand &getPredicateMutable();
  ::mlir::OpOperand &getLoadAddressMutable();
  ::mlir::OpOperand &getTileSliceIndexMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value load_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value load_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value load_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value load_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_ld1b_vert)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_ld1d_horiz declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_ld1d_horizGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_ld1d_horizGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_ld1d_horizGenericAdaptorBase(aarch64_sme_ld1d_horiz op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_ld1d_horizGenericAdaptor : public detail::aarch64_sme_ld1d_horizGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_ld1d_horizGenericAdaptorBase;
public:
  aarch64_sme_ld1d_horizGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_ld1d_horizGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_ld1d_horizGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_ld1d_horiz, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_ld1d_horiz>>>
  aarch64_sme_ld1d_horizGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getLoadAddress() {
    return (*getODSOperands(1).begin());
  }

  ValueT getTileSliceIndex() {
    return (*getODSOperands(2).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_ld1d_horizAdaptor : public aarch64_sme_ld1d_horizGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_ld1d_horizGenericAdaptor::aarch64_sme_ld1d_horizGenericAdaptor;
  aarch64_sme_ld1d_horizAdaptor(aarch64_sme_ld1d_horiz op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_ld1d_horiz : public ::mlir::Op<aarch64_sme_ld1d_horiz, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<3>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_ld1d_horizAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_ld1d_horizGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.ld1d.horiz");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getPredicate();
  ::mlir::TypedValue<::mlir::LLVM::LLVMPointerType> getLoadAddress();
  ::mlir::TypedValue<::mlir::IntegerType> getTileSliceIndex();
  ::mlir::OpOperand &getPredicateMutable();
  ::mlir::OpOperand &getLoadAddressMutable();
  ::mlir::OpOperand &getTileSliceIndexMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value load_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value load_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value load_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value load_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_ld1d_horiz)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_ld1d_vert declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_ld1d_vertGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_ld1d_vertGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_ld1d_vertGenericAdaptorBase(aarch64_sme_ld1d_vert op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_ld1d_vertGenericAdaptor : public detail::aarch64_sme_ld1d_vertGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_ld1d_vertGenericAdaptorBase;
public:
  aarch64_sme_ld1d_vertGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_ld1d_vertGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_ld1d_vertGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_ld1d_vert, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_ld1d_vert>>>
  aarch64_sme_ld1d_vertGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getLoadAddress() {
    return (*getODSOperands(1).begin());
  }

  ValueT getTileSliceIndex() {
    return (*getODSOperands(2).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_ld1d_vertAdaptor : public aarch64_sme_ld1d_vertGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_ld1d_vertGenericAdaptor::aarch64_sme_ld1d_vertGenericAdaptor;
  aarch64_sme_ld1d_vertAdaptor(aarch64_sme_ld1d_vert op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_ld1d_vert : public ::mlir::Op<aarch64_sme_ld1d_vert, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<3>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_ld1d_vertAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_ld1d_vertGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.ld1d.vert");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getPredicate();
  ::mlir::TypedValue<::mlir::LLVM::LLVMPointerType> getLoadAddress();
  ::mlir::TypedValue<::mlir::IntegerType> getTileSliceIndex();
  ::mlir::OpOperand &getPredicateMutable();
  ::mlir::OpOperand &getLoadAddressMutable();
  ::mlir::OpOperand &getTileSliceIndexMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value load_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value load_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value load_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value load_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_ld1d_vert)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_ld1h_horiz declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_ld1h_horizGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_ld1h_horizGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_ld1h_horizGenericAdaptorBase(aarch64_sme_ld1h_horiz op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_ld1h_horizGenericAdaptor : public detail::aarch64_sme_ld1h_horizGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_ld1h_horizGenericAdaptorBase;
public:
  aarch64_sme_ld1h_horizGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_ld1h_horizGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_ld1h_horizGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_ld1h_horiz, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_ld1h_horiz>>>
  aarch64_sme_ld1h_horizGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getLoadAddress() {
    return (*getODSOperands(1).begin());
  }

  ValueT getTileSliceIndex() {
    return (*getODSOperands(2).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_ld1h_horizAdaptor : public aarch64_sme_ld1h_horizGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_ld1h_horizGenericAdaptor::aarch64_sme_ld1h_horizGenericAdaptor;
  aarch64_sme_ld1h_horizAdaptor(aarch64_sme_ld1h_horiz op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_ld1h_horiz : public ::mlir::Op<aarch64_sme_ld1h_horiz, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<3>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_ld1h_horizAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_ld1h_horizGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.ld1h.horiz");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getPredicate();
  ::mlir::TypedValue<::mlir::LLVM::LLVMPointerType> getLoadAddress();
  ::mlir::TypedValue<::mlir::IntegerType> getTileSliceIndex();
  ::mlir::OpOperand &getPredicateMutable();
  ::mlir::OpOperand &getLoadAddressMutable();
  ::mlir::OpOperand &getTileSliceIndexMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value load_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value load_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value load_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value load_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_ld1h_horiz)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_ld1h_vert declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_ld1h_vertGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_ld1h_vertGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_ld1h_vertGenericAdaptorBase(aarch64_sme_ld1h_vert op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_ld1h_vertGenericAdaptor : public detail::aarch64_sme_ld1h_vertGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_ld1h_vertGenericAdaptorBase;
public:
  aarch64_sme_ld1h_vertGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_ld1h_vertGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_ld1h_vertGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_ld1h_vert, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_ld1h_vert>>>
  aarch64_sme_ld1h_vertGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getLoadAddress() {
    return (*getODSOperands(1).begin());
  }

  ValueT getTileSliceIndex() {
    return (*getODSOperands(2).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_ld1h_vertAdaptor : public aarch64_sme_ld1h_vertGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_ld1h_vertGenericAdaptor::aarch64_sme_ld1h_vertGenericAdaptor;
  aarch64_sme_ld1h_vertAdaptor(aarch64_sme_ld1h_vert op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_ld1h_vert : public ::mlir::Op<aarch64_sme_ld1h_vert, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<3>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_ld1h_vertAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_ld1h_vertGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.ld1h.vert");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getPredicate();
  ::mlir::TypedValue<::mlir::LLVM::LLVMPointerType> getLoadAddress();
  ::mlir::TypedValue<::mlir::IntegerType> getTileSliceIndex();
  ::mlir::OpOperand &getPredicateMutable();
  ::mlir::OpOperand &getLoadAddressMutable();
  ::mlir::OpOperand &getTileSliceIndexMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value load_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value load_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value load_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value load_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_ld1h_vert)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_ld1q_horiz declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_ld1q_horizGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_ld1q_horizGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_ld1q_horizGenericAdaptorBase(aarch64_sme_ld1q_horiz op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_ld1q_horizGenericAdaptor : public detail::aarch64_sme_ld1q_horizGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_ld1q_horizGenericAdaptorBase;
public:
  aarch64_sme_ld1q_horizGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_ld1q_horizGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_ld1q_horizGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_ld1q_horiz, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_ld1q_horiz>>>
  aarch64_sme_ld1q_horizGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getLoadAddress() {
    return (*getODSOperands(1).begin());
  }

  ValueT getTileSliceIndex() {
    return (*getODSOperands(2).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_ld1q_horizAdaptor : public aarch64_sme_ld1q_horizGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_ld1q_horizGenericAdaptor::aarch64_sme_ld1q_horizGenericAdaptor;
  aarch64_sme_ld1q_horizAdaptor(aarch64_sme_ld1q_horiz op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_ld1q_horiz : public ::mlir::Op<aarch64_sme_ld1q_horiz, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<3>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_ld1q_horizAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_ld1q_horizGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.ld1q.horiz");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getPredicate();
  ::mlir::TypedValue<::mlir::LLVM::LLVMPointerType> getLoadAddress();
  ::mlir::TypedValue<::mlir::IntegerType> getTileSliceIndex();
  ::mlir::OpOperand &getPredicateMutable();
  ::mlir::OpOperand &getLoadAddressMutable();
  ::mlir::OpOperand &getTileSliceIndexMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value load_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value load_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value load_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value load_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_ld1q_horiz)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_ld1q_vert declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_ld1q_vertGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_ld1q_vertGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_ld1q_vertGenericAdaptorBase(aarch64_sme_ld1q_vert op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_ld1q_vertGenericAdaptor : public detail::aarch64_sme_ld1q_vertGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_ld1q_vertGenericAdaptorBase;
public:
  aarch64_sme_ld1q_vertGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_ld1q_vertGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_ld1q_vertGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_ld1q_vert, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_ld1q_vert>>>
  aarch64_sme_ld1q_vertGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getLoadAddress() {
    return (*getODSOperands(1).begin());
  }

  ValueT getTileSliceIndex() {
    return (*getODSOperands(2).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_ld1q_vertAdaptor : public aarch64_sme_ld1q_vertGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_ld1q_vertGenericAdaptor::aarch64_sme_ld1q_vertGenericAdaptor;
  aarch64_sme_ld1q_vertAdaptor(aarch64_sme_ld1q_vert op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_ld1q_vert : public ::mlir::Op<aarch64_sme_ld1q_vert, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<3>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_ld1q_vertAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_ld1q_vertGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.ld1q.vert");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getPredicate();
  ::mlir::TypedValue<::mlir::LLVM::LLVMPointerType> getLoadAddress();
  ::mlir::TypedValue<::mlir::IntegerType> getTileSliceIndex();
  ::mlir::OpOperand &getPredicateMutable();
  ::mlir::OpOperand &getLoadAddressMutable();
  ::mlir::OpOperand &getTileSliceIndexMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value load_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value load_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value load_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value load_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_ld1q_vert)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_ld1w_horiz declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_ld1w_horizGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_ld1w_horizGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_ld1w_horizGenericAdaptorBase(aarch64_sme_ld1w_horiz op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_ld1w_horizGenericAdaptor : public detail::aarch64_sme_ld1w_horizGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_ld1w_horizGenericAdaptorBase;
public:
  aarch64_sme_ld1w_horizGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_ld1w_horizGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_ld1w_horizGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_ld1w_horiz, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_ld1w_horiz>>>
  aarch64_sme_ld1w_horizGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getLoadAddress() {
    return (*getODSOperands(1).begin());
  }

  ValueT getTileSliceIndex() {
    return (*getODSOperands(2).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_ld1w_horizAdaptor : public aarch64_sme_ld1w_horizGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_ld1w_horizGenericAdaptor::aarch64_sme_ld1w_horizGenericAdaptor;
  aarch64_sme_ld1w_horizAdaptor(aarch64_sme_ld1w_horiz op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_ld1w_horiz : public ::mlir::Op<aarch64_sme_ld1w_horiz, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<3>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_ld1w_horizAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_ld1w_horizGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.ld1w.horiz");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getPredicate();
  ::mlir::TypedValue<::mlir::LLVM::LLVMPointerType> getLoadAddress();
  ::mlir::TypedValue<::mlir::IntegerType> getTileSliceIndex();
  ::mlir::OpOperand &getPredicateMutable();
  ::mlir::OpOperand &getLoadAddressMutable();
  ::mlir::OpOperand &getTileSliceIndexMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value load_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value load_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value load_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value load_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_ld1w_horiz)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_ld1w_vert declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_ld1w_vertGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_ld1w_vertGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_ld1w_vertGenericAdaptorBase(aarch64_sme_ld1w_vert op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_ld1w_vertGenericAdaptor : public detail::aarch64_sme_ld1w_vertGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_ld1w_vertGenericAdaptorBase;
public:
  aarch64_sme_ld1w_vertGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_ld1w_vertGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_ld1w_vertGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_ld1w_vert, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_ld1w_vert>>>
  aarch64_sme_ld1w_vertGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getLoadAddress() {
    return (*getODSOperands(1).begin());
  }

  ValueT getTileSliceIndex() {
    return (*getODSOperands(2).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_ld1w_vertAdaptor : public aarch64_sme_ld1w_vertGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_ld1w_vertGenericAdaptor::aarch64_sme_ld1w_vertGenericAdaptor;
  aarch64_sme_ld1w_vertAdaptor(aarch64_sme_ld1w_vert op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_ld1w_vert : public ::mlir::Op<aarch64_sme_ld1w_vert, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<3>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_ld1w_vertAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_ld1w_vertGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.ld1w.vert");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getPredicate();
  ::mlir::TypedValue<::mlir::LLVM::LLVMPointerType> getLoadAddress();
  ::mlir::TypedValue<::mlir::IntegerType> getTileSliceIndex();
  ::mlir::OpOperand &getPredicateMutable();
  ::mlir::OpOperand &getLoadAddressMutable();
  ::mlir::OpOperand &getTileSliceIndexMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value load_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value load_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value load_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value load_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_ld1w_vert)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_mopa declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_mopaGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_mopaGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_mopaGenericAdaptorBase(aarch64_sme_mopa op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_mopaGenericAdaptor : public detail::aarch64_sme_mopaGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_mopaGenericAdaptorBase;
public:
  aarch64_sme_mopaGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_mopaGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_mopaGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_mopa, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_mopa>>>
  aarch64_sme_mopaGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getLhsPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getRhsPredicate() {
    return (*getODSOperands(1).begin());
  }

  ValueT getLhsVector() {
    return (*getODSOperands(2).begin());
  }

  ValueT getRhsVector() {
    return (*getODSOperands(3).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_mopaAdaptor : public aarch64_sme_mopaGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_mopaGenericAdaptor::aarch64_sme_mopaGenericAdaptor;
  aarch64_sme_mopaAdaptor(aarch64_sme_mopa op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_mopa : public ::mlir::Op<aarch64_sme_mopa, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<4>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_mopaAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_mopaGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.mopa");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getLhsPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getRhsPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getLhsVector();
  ::mlir::TypedValue<::mlir::VectorType> getRhsVector();
  ::mlir::OpOperand &getLhsPredicateMutable();
  ::mlir::OpOperand &getRhsPredicateMutable();
  ::mlir::OpOperand &getLhsVectorMutable();
  ::mlir::OpOperand &getRhsVectorMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::IntegerAttr tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::IntegerAttr tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, uint32_t tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, uint32_t tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_mopa)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_mopa_wide declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_mopa_wideGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_mopa_wideGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_mopa_wideGenericAdaptorBase(aarch64_sme_mopa_wide op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_mopa_wideGenericAdaptor : public detail::aarch64_sme_mopa_wideGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_mopa_wideGenericAdaptorBase;
public:
  aarch64_sme_mopa_wideGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_mopa_wideGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_mopa_wideGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_mopa_wide, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_mopa_wide>>>
  aarch64_sme_mopa_wideGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getLhsPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getRhsPredicate() {
    return (*getODSOperands(1).begin());
  }

  ValueT getLhsVector() {
    return (*getODSOperands(2).begin());
  }

  ValueT getRhsVector() {
    return (*getODSOperands(3).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_mopa_wideAdaptor : public aarch64_sme_mopa_wideGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_mopa_wideGenericAdaptor::aarch64_sme_mopa_wideGenericAdaptor;
  aarch64_sme_mopa_wideAdaptor(aarch64_sme_mopa_wide op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_mopa_wide : public ::mlir::Op<aarch64_sme_mopa_wide, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<4>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_mopa_wideAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_mopa_wideGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.mopa.wide");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getLhsPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getRhsPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getLhsVector();
  ::mlir::TypedValue<::mlir::VectorType> getRhsVector();
  ::mlir::OpOperand &getLhsPredicateMutable();
  ::mlir::OpOperand &getRhsPredicateMutable();
  ::mlir::OpOperand &getLhsVectorMutable();
  ::mlir::OpOperand &getRhsVectorMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::IntegerAttr tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::IntegerAttr tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, uint32_t tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, uint32_t tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_mopa_wide)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_mops declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_mopsGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_mopsGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_mopsGenericAdaptorBase(aarch64_sme_mops op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_mopsGenericAdaptor : public detail::aarch64_sme_mopsGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_mopsGenericAdaptorBase;
public:
  aarch64_sme_mopsGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_mopsGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_mopsGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_mops, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_mops>>>
  aarch64_sme_mopsGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getLhsPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getRhsPredicate() {
    return (*getODSOperands(1).begin());
  }

  ValueT getLhsVector() {
    return (*getODSOperands(2).begin());
  }

  ValueT getRhsVector() {
    return (*getODSOperands(3).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_mopsAdaptor : public aarch64_sme_mopsGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_mopsGenericAdaptor::aarch64_sme_mopsGenericAdaptor;
  aarch64_sme_mopsAdaptor(aarch64_sme_mops op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_mops : public ::mlir::Op<aarch64_sme_mops, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<4>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_mopsAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_mopsGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.mops");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getLhsPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getRhsPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getLhsVector();
  ::mlir::TypedValue<::mlir::VectorType> getRhsVector();
  ::mlir::OpOperand &getLhsPredicateMutable();
  ::mlir::OpOperand &getRhsPredicateMutable();
  ::mlir::OpOperand &getLhsVectorMutable();
  ::mlir::OpOperand &getRhsVectorMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::IntegerAttr tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::IntegerAttr tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, uint32_t tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, uint32_t tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_mops)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_mops_wide declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_mops_wideGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_mops_wideGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_mops_wideGenericAdaptorBase(aarch64_sme_mops_wide op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_mops_wideGenericAdaptor : public detail::aarch64_sme_mops_wideGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_mops_wideGenericAdaptorBase;
public:
  aarch64_sme_mops_wideGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_mops_wideGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_mops_wideGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_mops_wide, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_mops_wide>>>
  aarch64_sme_mops_wideGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getLhsPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getRhsPredicate() {
    return (*getODSOperands(1).begin());
  }

  ValueT getLhsVector() {
    return (*getODSOperands(2).begin());
  }

  ValueT getRhsVector() {
    return (*getODSOperands(3).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_mops_wideAdaptor : public aarch64_sme_mops_wideGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_mops_wideGenericAdaptor::aarch64_sme_mops_wideGenericAdaptor;
  aarch64_sme_mops_wideAdaptor(aarch64_sme_mops_wide op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_mops_wide : public ::mlir::Op<aarch64_sme_mops_wide, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<4>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_mops_wideAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_mops_wideGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.mops.wide");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getLhsPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getRhsPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getLhsVector();
  ::mlir::TypedValue<::mlir::VectorType> getRhsVector();
  ::mlir::OpOperand &getLhsPredicateMutable();
  ::mlir::OpOperand &getRhsPredicateMutable();
  ::mlir::OpOperand &getLhsVectorMutable();
  ::mlir::OpOperand &getRhsVectorMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::IntegerAttr tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::IntegerAttr tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, uint32_t tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, uint32_t tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_mops_wide)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_read_horiz declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_read_horizGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_read_horizGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_read_horizGenericAdaptorBase(aarch64_sme_read_horiz op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_read_horizGenericAdaptor : public detail::aarch64_sme_read_horizGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_read_horizGenericAdaptorBase;
public:
  aarch64_sme_read_horizGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_read_horizGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_read_horizGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_read_horiz, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_read_horiz>>>
  aarch64_sme_read_horizGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getVector() {
    return (*getODSOperands(0).begin());
  }

  ValueT getPredicate() {
    return (*getODSOperands(1).begin());
  }

  ValueT getTileSliceIndex() {
    return (*getODSOperands(2).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_read_horizAdaptor : public aarch64_sme_read_horizGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_read_horizGenericAdaptor::aarch64_sme_read_horizGenericAdaptor;
  aarch64_sme_read_horizAdaptor(aarch64_sme_read_horiz op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_read_horiz : public ::mlir::Op<aarch64_sme_read_horiz, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::OneResult, ::mlir::OpTrait::OneTypedResult<::mlir::Type>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<3>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_read_horizAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_read_horizGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.read.horiz");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getVector();
  ::mlir::TypedValue<::mlir::VectorType> getPredicate();
  ::mlir::TypedValue<::mlir::IntegerType> getTileSliceIndex();
  ::mlir::OpOperand &getVectorMutable();
  ::mlir::OpOperand &getPredicateMutable();
  ::mlir::OpOperand &getTileSliceIndexMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  ::mlir::Value getRes();
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type res, ::mlir::Value vector, ::mlir::Value predicate, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value vector, ::mlir::Value predicate, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type res, ::mlir::Value vector, ::mlir::Value predicate, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value vector, ::mlir::Value predicate, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_read_horiz)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_read_vert declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_read_vertGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_read_vertGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_read_vertGenericAdaptorBase(aarch64_sme_read_vert op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_read_vertGenericAdaptor : public detail::aarch64_sme_read_vertGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_read_vertGenericAdaptorBase;
public:
  aarch64_sme_read_vertGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_read_vertGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_read_vertGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_read_vert, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_read_vert>>>
  aarch64_sme_read_vertGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getVector() {
    return (*getODSOperands(0).begin());
  }

  ValueT getPredicate() {
    return (*getODSOperands(1).begin());
  }

  ValueT getTileSliceIndex() {
    return (*getODSOperands(2).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_read_vertAdaptor : public aarch64_sme_read_vertGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_read_vertGenericAdaptor::aarch64_sme_read_vertGenericAdaptor;
  aarch64_sme_read_vertAdaptor(aarch64_sme_read_vert op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_read_vert : public ::mlir::Op<aarch64_sme_read_vert, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::OneResult, ::mlir::OpTrait::OneTypedResult<::mlir::Type>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<3>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_read_vertAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_read_vertGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.read.vert");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getVector();
  ::mlir::TypedValue<::mlir::VectorType> getPredicate();
  ::mlir::TypedValue<::mlir::IntegerType> getTileSliceIndex();
  ::mlir::OpOperand &getVectorMutable();
  ::mlir::OpOperand &getPredicateMutable();
  ::mlir::OpOperand &getTileSliceIndexMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  ::mlir::Value getRes();
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type res, ::mlir::Value vector, ::mlir::Value predicate, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value vector, ::mlir::Value predicate, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type res, ::mlir::Value vector, ::mlir::Value predicate, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value vector, ::mlir::Value predicate, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_read_vert)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_smopa_wide declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_smopa_wideGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_smopa_wideGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_smopa_wideGenericAdaptorBase(aarch64_sme_smopa_wide op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_smopa_wideGenericAdaptor : public detail::aarch64_sme_smopa_wideGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_smopa_wideGenericAdaptorBase;
public:
  aarch64_sme_smopa_wideGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_smopa_wideGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_smopa_wideGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_smopa_wide, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_smopa_wide>>>
  aarch64_sme_smopa_wideGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getLhsPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getRhsPredicate() {
    return (*getODSOperands(1).begin());
  }

  ValueT getLhsVector() {
    return (*getODSOperands(2).begin());
  }

  ValueT getRhsVector() {
    return (*getODSOperands(3).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_smopa_wideAdaptor : public aarch64_sme_smopa_wideGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_smopa_wideGenericAdaptor::aarch64_sme_smopa_wideGenericAdaptor;
  aarch64_sme_smopa_wideAdaptor(aarch64_sme_smopa_wide op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_smopa_wide : public ::mlir::Op<aarch64_sme_smopa_wide, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<4>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_smopa_wideAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_smopa_wideGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.smopa.wide");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getLhsPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getRhsPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getLhsVector();
  ::mlir::TypedValue<::mlir::VectorType> getRhsVector();
  ::mlir::OpOperand &getLhsPredicateMutable();
  ::mlir::OpOperand &getRhsPredicateMutable();
  ::mlir::OpOperand &getLhsVectorMutable();
  ::mlir::OpOperand &getRhsVectorMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::IntegerAttr tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::IntegerAttr tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, uint32_t tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, uint32_t tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_smopa_wide)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_smopa_za32 declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_smopa_za32GenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_smopa_za32GenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_smopa_za32GenericAdaptorBase(aarch64_sme_smopa_za32 op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_smopa_za32GenericAdaptor : public detail::aarch64_sme_smopa_za32GenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_smopa_za32GenericAdaptorBase;
public:
  aarch64_sme_smopa_za32GenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_smopa_za32GenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_smopa_za32GenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_smopa_za32, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_smopa_za32>>>
  aarch64_sme_smopa_za32GenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getLhsPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getRhsPredicate() {
    return (*getODSOperands(1).begin());
  }

  ValueT getLhsVector() {
    return (*getODSOperands(2).begin());
  }

  ValueT getRhsVector() {
    return (*getODSOperands(3).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_smopa_za32Adaptor : public aarch64_sme_smopa_za32GenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_smopa_za32GenericAdaptor::aarch64_sme_smopa_za32GenericAdaptor;
  aarch64_sme_smopa_za32Adaptor(aarch64_sme_smopa_za32 op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_smopa_za32 : public ::mlir::Op<aarch64_sme_smopa_za32, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<4>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_smopa_za32Adaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_smopa_za32GenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.smopa.za32");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getLhsPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getRhsPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getLhsVector();
  ::mlir::TypedValue<::mlir::VectorType> getRhsVector();
  ::mlir::OpOperand &getLhsPredicateMutable();
  ::mlir::OpOperand &getRhsPredicateMutable();
  ::mlir::OpOperand &getLhsVectorMutable();
  ::mlir::OpOperand &getRhsVectorMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::IntegerAttr tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::IntegerAttr tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, uint32_t tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, uint32_t tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_smopa_za32)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_smops_wide declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_smops_wideGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_smops_wideGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_smops_wideGenericAdaptorBase(aarch64_sme_smops_wide op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_smops_wideGenericAdaptor : public detail::aarch64_sme_smops_wideGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_smops_wideGenericAdaptorBase;
public:
  aarch64_sme_smops_wideGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_smops_wideGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_smops_wideGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_smops_wide, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_smops_wide>>>
  aarch64_sme_smops_wideGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getLhsPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getRhsPredicate() {
    return (*getODSOperands(1).begin());
  }

  ValueT getLhsVector() {
    return (*getODSOperands(2).begin());
  }

  ValueT getRhsVector() {
    return (*getODSOperands(3).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_smops_wideAdaptor : public aarch64_sme_smops_wideGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_smops_wideGenericAdaptor::aarch64_sme_smops_wideGenericAdaptor;
  aarch64_sme_smops_wideAdaptor(aarch64_sme_smops_wide op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_smops_wide : public ::mlir::Op<aarch64_sme_smops_wide, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<4>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_smops_wideAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_smops_wideGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.smops.wide");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getLhsPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getRhsPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getLhsVector();
  ::mlir::TypedValue<::mlir::VectorType> getRhsVector();
  ::mlir::OpOperand &getLhsPredicateMutable();
  ::mlir::OpOperand &getRhsPredicateMutable();
  ::mlir::OpOperand &getLhsVectorMutable();
  ::mlir::OpOperand &getRhsVectorMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::IntegerAttr tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::IntegerAttr tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, uint32_t tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, uint32_t tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_smops_wide)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_smops_za32 declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_smops_za32GenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_smops_za32GenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_smops_za32GenericAdaptorBase(aarch64_sme_smops_za32 op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_smops_za32GenericAdaptor : public detail::aarch64_sme_smops_za32GenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_smops_za32GenericAdaptorBase;
public:
  aarch64_sme_smops_za32GenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_smops_za32GenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_smops_za32GenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_smops_za32, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_smops_za32>>>
  aarch64_sme_smops_za32GenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getLhsPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getRhsPredicate() {
    return (*getODSOperands(1).begin());
  }

  ValueT getLhsVector() {
    return (*getODSOperands(2).begin());
  }

  ValueT getRhsVector() {
    return (*getODSOperands(3).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_smops_za32Adaptor : public aarch64_sme_smops_za32GenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_smops_za32GenericAdaptor::aarch64_sme_smops_za32GenericAdaptor;
  aarch64_sme_smops_za32Adaptor(aarch64_sme_smops_za32 op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_smops_za32 : public ::mlir::Op<aarch64_sme_smops_za32, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<4>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_smops_za32Adaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_smops_za32GenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.smops.za32");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getLhsPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getRhsPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getLhsVector();
  ::mlir::TypedValue<::mlir::VectorType> getRhsVector();
  ::mlir::OpOperand &getLhsPredicateMutable();
  ::mlir::OpOperand &getRhsPredicateMutable();
  ::mlir::OpOperand &getLhsVectorMutable();
  ::mlir::OpOperand &getRhsVectorMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::IntegerAttr tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::IntegerAttr tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, uint32_t tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, uint32_t tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_smops_za32)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_st1b_horiz declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_st1b_horizGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_st1b_horizGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_st1b_horizGenericAdaptorBase(aarch64_sme_st1b_horiz op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_st1b_horizGenericAdaptor : public detail::aarch64_sme_st1b_horizGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_st1b_horizGenericAdaptorBase;
public:
  aarch64_sme_st1b_horizGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_st1b_horizGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_st1b_horizGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_st1b_horiz, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_st1b_horiz>>>
  aarch64_sme_st1b_horizGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getStoreAddress() {
    return (*getODSOperands(1).begin());
  }

  ValueT getTileSliceIndex() {
    return (*getODSOperands(2).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_st1b_horizAdaptor : public aarch64_sme_st1b_horizGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_st1b_horizGenericAdaptor::aarch64_sme_st1b_horizGenericAdaptor;
  aarch64_sme_st1b_horizAdaptor(aarch64_sme_st1b_horiz op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_st1b_horiz : public ::mlir::Op<aarch64_sme_st1b_horiz, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<3>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::MemoryEffectOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_st1b_horizAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_st1b_horizGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.st1b.horiz");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getPredicate();
  ::mlir::TypedValue<::mlir::LLVM::LLVMPointerType> getStoreAddress();
  ::mlir::TypedValue<::mlir::IntegerType> getTileSliceIndex();
  ::mlir::OpOperand &getPredicateMutable();
  ::mlir::OpOperand &getStoreAddressMutable();
  ::mlir::OpOperand &getTileSliceIndexMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value store_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value store_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value store_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value store_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_st1b_horiz)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_st1b_vert declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_st1b_vertGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_st1b_vertGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_st1b_vertGenericAdaptorBase(aarch64_sme_st1b_vert op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_st1b_vertGenericAdaptor : public detail::aarch64_sme_st1b_vertGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_st1b_vertGenericAdaptorBase;
public:
  aarch64_sme_st1b_vertGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_st1b_vertGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_st1b_vertGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_st1b_vert, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_st1b_vert>>>
  aarch64_sme_st1b_vertGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getStoreAddress() {
    return (*getODSOperands(1).begin());
  }

  ValueT getTileSliceIndex() {
    return (*getODSOperands(2).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_st1b_vertAdaptor : public aarch64_sme_st1b_vertGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_st1b_vertGenericAdaptor::aarch64_sme_st1b_vertGenericAdaptor;
  aarch64_sme_st1b_vertAdaptor(aarch64_sme_st1b_vert op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_st1b_vert : public ::mlir::Op<aarch64_sme_st1b_vert, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<3>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::MemoryEffectOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_st1b_vertAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_st1b_vertGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.st1b.vert");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getPredicate();
  ::mlir::TypedValue<::mlir::LLVM::LLVMPointerType> getStoreAddress();
  ::mlir::TypedValue<::mlir::IntegerType> getTileSliceIndex();
  ::mlir::OpOperand &getPredicateMutable();
  ::mlir::OpOperand &getStoreAddressMutable();
  ::mlir::OpOperand &getTileSliceIndexMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value store_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value store_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value store_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value store_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_st1b_vert)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_st1d_horiz declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_st1d_horizGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_st1d_horizGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_st1d_horizGenericAdaptorBase(aarch64_sme_st1d_horiz op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_st1d_horizGenericAdaptor : public detail::aarch64_sme_st1d_horizGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_st1d_horizGenericAdaptorBase;
public:
  aarch64_sme_st1d_horizGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_st1d_horizGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_st1d_horizGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_st1d_horiz, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_st1d_horiz>>>
  aarch64_sme_st1d_horizGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getStoreAddress() {
    return (*getODSOperands(1).begin());
  }

  ValueT getTileSliceIndex() {
    return (*getODSOperands(2).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_st1d_horizAdaptor : public aarch64_sme_st1d_horizGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_st1d_horizGenericAdaptor::aarch64_sme_st1d_horizGenericAdaptor;
  aarch64_sme_st1d_horizAdaptor(aarch64_sme_st1d_horiz op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_st1d_horiz : public ::mlir::Op<aarch64_sme_st1d_horiz, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<3>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::MemoryEffectOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_st1d_horizAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_st1d_horizGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.st1d.horiz");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getPredicate();
  ::mlir::TypedValue<::mlir::LLVM::LLVMPointerType> getStoreAddress();
  ::mlir::TypedValue<::mlir::IntegerType> getTileSliceIndex();
  ::mlir::OpOperand &getPredicateMutable();
  ::mlir::OpOperand &getStoreAddressMutable();
  ::mlir::OpOperand &getTileSliceIndexMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value store_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value store_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value store_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value store_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_st1d_horiz)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_st1d_vert declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_st1d_vertGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_st1d_vertGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_st1d_vertGenericAdaptorBase(aarch64_sme_st1d_vert op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_st1d_vertGenericAdaptor : public detail::aarch64_sme_st1d_vertGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_st1d_vertGenericAdaptorBase;
public:
  aarch64_sme_st1d_vertGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_st1d_vertGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_st1d_vertGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_st1d_vert, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_st1d_vert>>>
  aarch64_sme_st1d_vertGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getStoreAddress() {
    return (*getODSOperands(1).begin());
  }

  ValueT getTileSliceIndex() {
    return (*getODSOperands(2).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_st1d_vertAdaptor : public aarch64_sme_st1d_vertGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_st1d_vertGenericAdaptor::aarch64_sme_st1d_vertGenericAdaptor;
  aarch64_sme_st1d_vertAdaptor(aarch64_sme_st1d_vert op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_st1d_vert : public ::mlir::Op<aarch64_sme_st1d_vert, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<3>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::MemoryEffectOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_st1d_vertAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_st1d_vertGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.st1d.vert");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getPredicate();
  ::mlir::TypedValue<::mlir::LLVM::LLVMPointerType> getStoreAddress();
  ::mlir::TypedValue<::mlir::IntegerType> getTileSliceIndex();
  ::mlir::OpOperand &getPredicateMutable();
  ::mlir::OpOperand &getStoreAddressMutable();
  ::mlir::OpOperand &getTileSliceIndexMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value store_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value store_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value store_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value store_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_st1d_vert)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_st1h_horiz declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_st1h_horizGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_st1h_horizGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_st1h_horizGenericAdaptorBase(aarch64_sme_st1h_horiz op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_st1h_horizGenericAdaptor : public detail::aarch64_sme_st1h_horizGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_st1h_horizGenericAdaptorBase;
public:
  aarch64_sme_st1h_horizGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_st1h_horizGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_st1h_horizGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_st1h_horiz, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_st1h_horiz>>>
  aarch64_sme_st1h_horizGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getStoreAddress() {
    return (*getODSOperands(1).begin());
  }

  ValueT getTileSliceIndex() {
    return (*getODSOperands(2).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_st1h_horizAdaptor : public aarch64_sme_st1h_horizGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_st1h_horizGenericAdaptor::aarch64_sme_st1h_horizGenericAdaptor;
  aarch64_sme_st1h_horizAdaptor(aarch64_sme_st1h_horiz op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_st1h_horiz : public ::mlir::Op<aarch64_sme_st1h_horiz, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<3>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::MemoryEffectOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_st1h_horizAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_st1h_horizGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.st1h.horiz");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getPredicate();
  ::mlir::TypedValue<::mlir::LLVM::LLVMPointerType> getStoreAddress();
  ::mlir::TypedValue<::mlir::IntegerType> getTileSliceIndex();
  ::mlir::OpOperand &getPredicateMutable();
  ::mlir::OpOperand &getStoreAddressMutable();
  ::mlir::OpOperand &getTileSliceIndexMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value store_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value store_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value store_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value store_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_st1h_horiz)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_st1h_vert declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_st1h_vertGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_st1h_vertGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_st1h_vertGenericAdaptorBase(aarch64_sme_st1h_vert op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_st1h_vertGenericAdaptor : public detail::aarch64_sme_st1h_vertGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_st1h_vertGenericAdaptorBase;
public:
  aarch64_sme_st1h_vertGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_st1h_vertGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_st1h_vertGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_st1h_vert, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_st1h_vert>>>
  aarch64_sme_st1h_vertGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getStoreAddress() {
    return (*getODSOperands(1).begin());
  }

  ValueT getTileSliceIndex() {
    return (*getODSOperands(2).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_st1h_vertAdaptor : public aarch64_sme_st1h_vertGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_st1h_vertGenericAdaptor::aarch64_sme_st1h_vertGenericAdaptor;
  aarch64_sme_st1h_vertAdaptor(aarch64_sme_st1h_vert op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_st1h_vert : public ::mlir::Op<aarch64_sme_st1h_vert, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<3>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::MemoryEffectOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_st1h_vertAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_st1h_vertGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.st1h.vert");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getPredicate();
  ::mlir::TypedValue<::mlir::LLVM::LLVMPointerType> getStoreAddress();
  ::mlir::TypedValue<::mlir::IntegerType> getTileSliceIndex();
  ::mlir::OpOperand &getPredicateMutable();
  ::mlir::OpOperand &getStoreAddressMutable();
  ::mlir::OpOperand &getTileSliceIndexMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value store_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value store_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value store_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value store_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_st1h_vert)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_st1q_horiz declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_st1q_horizGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_st1q_horizGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_st1q_horizGenericAdaptorBase(aarch64_sme_st1q_horiz op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_st1q_horizGenericAdaptor : public detail::aarch64_sme_st1q_horizGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_st1q_horizGenericAdaptorBase;
public:
  aarch64_sme_st1q_horizGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_st1q_horizGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_st1q_horizGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_st1q_horiz, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_st1q_horiz>>>
  aarch64_sme_st1q_horizGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getStoreAddress() {
    return (*getODSOperands(1).begin());
  }

  ValueT getTileSliceIndex() {
    return (*getODSOperands(2).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_st1q_horizAdaptor : public aarch64_sme_st1q_horizGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_st1q_horizGenericAdaptor::aarch64_sme_st1q_horizGenericAdaptor;
  aarch64_sme_st1q_horizAdaptor(aarch64_sme_st1q_horiz op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_st1q_horiz : public ::mlir::Op<aarch64_sme_st1q_horiz, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<3>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::MemoryEffectOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_st1q_horizAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_st1q_horizGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.st1q.horiz");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getPredicate();
  ::mlir::TypedValue<::mlir::LLVM::LLVMPointerType> getStoreAddress();
  ::mlir::TypedValue<::mlir::IntegerType> getTileSliceIndex();
  ::mlir::OpOperand &getPredicateMutable();
  ::mlir::OpOperand &getStoreAddressMutable();
  ::mlir::OpOperand &getTileSliceIndexMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value store_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value store_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value store_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value store_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_st1q_horiz)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_st1q_vert declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_st1q_vertGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_st1q_vertGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_st1q_vertGenericAdaptorBase(aarch64_sme_st1q_vert op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_st1q_vertGenericAdaptor : public detail::aarch64_sme_st1q_vertGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_st1q_vertGenericAdaptorBase;
public:
  aarch64_sme_st1q_vertGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_st1q_vertGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_st1q_vertGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_st1q_vert, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_st1q_vert>>>
  aarch64_sme_st1q_vertGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getStoreAddress() {
    return (*getODSOperands(1).begin());
  }

  ValueT getTileSliceIndex() {
    return (*getODSOperands(2).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_st1q_vertAdaptor : public aarch64_sme_st1q_vertGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_st1q_vertGenericAdaptor::aarch64_sme_st1q_vertGenericAdaptor;
  aarch64_sme_st1q_vertAdaptor(aarch64_sme_st1q_vert op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_st1q_vert : public ::mlir::Op<aarch64_sme_st1q_vert, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<3>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::MemoryEffectOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_st1q_vertAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_st1q_vertGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.st1q.vert");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getPredicate();
  ::mlir::TypedValue<::mlir::LLVM::LLVMPointerType> getStoreAddress();
  ::mlir::TypedValue<::mlir::IntegerType> getTileSliceIndex();
  ::mlir::OpOperand &getPredicateMutable();
  ::mlir::OpOperand &getStoreAddressMutable();
  ::mlir::OpOperand &getTileSliceIndexMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value store_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value store_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value store_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value store_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_st1q_vert)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_st1w_horiz declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_st1w_horizGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_st1w_horizGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_st1w_horizGenericAdaptorBase(aarch64_sme_st1w_horiz op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_st1w_horizGenericAdaptor : public detail::aarch64_sme_st1w_horizGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_st1w_horizGenericAdaptorBase;
public:
  aarch64_sme_st1w_horizGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_st1w_horizGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_st1w_horizGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_st1w_horiz, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_st1w_horiz>>>
  aarch64_sme_st1w_horizGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getStoreAddress() {
    return (*getODSOperands(1).begin());
  }

  ValueT getTileSliceIndex() {
    return (*getODSOperands(2).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_st1w_horizAdaptor : public aarch64_sme_st1w_horizGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_st1w_horizGenericAdaptor::aarch64_sme_st1w_horizGenericAdaptor;
  aarch64_sme_st1w_horizAdaptor(aarch64_sme_st1w_horiz op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_st1w_horiz : public ::mlir::Op<aarch64_sme_st1w_horiz, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<3>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::MemoryEffectOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_st1w_horizAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_st1w_horizGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.st1w.horiz");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getPredicate();
  ::mlir::TypedValue<::mlir::LLVM::LLVMPointerType> getStoreAddress();
  ::mlir::TypedValue<::mlir::IntegerType> getTileSliceIndex();
  ::mlir::OpOperand &getPredicateMutable();
  ::mlir::OpOperand &getStoreAddressMutable();
  ::mlir::OpOperand &getTileSliceIndexMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value store_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value store_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value store_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value store_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_st1w_horiz)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_st1w_vert declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_st1w_vertGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_st1w_vertGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_st1w_vertGenericAdaptorBase(aarch64_sme_st1w_vert op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_st1w_vertGenericAdaptor : public detail::aarch64_sme_st1w_vertGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_st1w_vertGenericAdaptorBase;
public:
  aarch64_sme_st1w_vertGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_st1w_vertGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_st1w_vertGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_st1w_vert, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_st1w_vert>>>
  aarch64_sme_st1w_vertGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getStoreAddress() {
    return (*getODSOperands(1).begin());
  }

  ValueT getTileSliceIndex() {
    return (*getODSOperands(2).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_st1w_vertAdaptor : public aarch64_sme_st1w_vertGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_st1w_vertGenericAdaptor::aarch64_sme_st1w_vertGenericAdaptor;
  aarch64_sme_st1w_vertAdaptor(aarch64_sme_st1w_vert op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_st1w_vert : public ::mlir::Op<aarch64_sme_st1w_vert, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<3>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::MemoryEffectOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_st1w_vertAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_st1w_vertGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.st1w.vert");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getPredicate();
  ::mlir::TypedValue<::mlir::LLVM::LLVMPointerType> getStoreAddress();
  ::mlir::TypedValue<::mlir::IntegerType> getTileSliceIndex();
  ::mlir::OpOperand &getPredicateMutable();
  ::mlir::OpOperand &getStoreAddressMutable();
  ::mlir::OpOperand &getTileSliceIndexMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value store_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value store_address, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value predicate, ::mlir::Value store_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value predicate, ::mlir::Value store_address, uint32_t tile_id, ::mlir::Value tile_slice_index);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_st1w_vert)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_str declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_strGenericAdaptorBase {
public:
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_strGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const ::mlir::EmptyProperties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_strGenericAdaptorBase(aarch64_sme_str op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  ::mlir::DictionaryAttr getAttributes();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_strGenericAdaptor : public detail::aarch64_sme_strGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_strGenericAdaptorBase;
public:
  aarch64_sme_strGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const ::mlir::EmptyProperties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_strGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_strGenericAdaptor(values, attrs, (properties ? *properties.as<::mlir::EmptyProperties *>() : ::mlir::EmptyProperties{}), regions) {}

  template <typename LateInst = aarch64_sme_str, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_str>>>
  aarch64_sme_strGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getIndex() {
    return (*getODSOperands(0).begin());
  }

  ValueT getStoreAddress() {
    return (*getODSOperands(1).begin());
  }

  ValueT getOffset() {
    return (*getODSOperands(2).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_strAdaptor : public aarch64_sme_strGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_strGenericAdaptor::aarch64_sme_strGenericAdaptor;
  aarch64_sme_strAdaptor(aarch64_sme_str op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_str : public ::mlir::Op<aarch64_sme_str, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<3>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::MemoryEffectOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_strAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_strGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    return {};
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.str");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::IntegerType> getIndex();
  ::mlir::TypedValue<::mlir::LLVM::LLVMPointerType> getStoreAddress();
  ::mlir::TypedValue<::mlir::IntegerType> getOffset();
  ::mlir::OpOperand &getIndexMutable();
  ::mlir::OpOperand &getStoreAddressMutable();
  ::mlir::OpOperand &getOffsetMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value index, ::mlir::Value store_address, ::mlir::Value offset);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value index, ::mlir::Value store_address, ::mlir::Value offset);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_str)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_sumopa_wide declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_sumopa_wideGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_sumopa_wideGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_sumopa_wideGenericAdaptorBase(aarch64_sme_sumopa_wide op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_sumopa_wideGenericAdaptor : public detail::aarch64_sme_sumopa_wideGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_sumopa_wideGenericAdaptorBase;
public:
  aarch64_sme_sumopa_wideGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_sumopa_wideGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_sumopa_wideGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_sumopa_wide, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_sumopa_wide>>>
  aarch64_sme_sumopa_wideGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getLhsPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getRhsPredicate() {
    return (*getODSOperands(1).begin());
  }

  ValueT getLhsVector() {
    return (*getODSOperands(2).begin());
  }

  ValueT getRhsVector() {
    return (*getODSOperands(3).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_sumopa_wideAdaptor : public aarch64_sme_sumopa_wideGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_sumopa_wideGenericAdaptor::aarch64_sme_sumopa_wideGenericAdaptor;
  aarch64_sme_sumopa_wideAdaptor(aarch64_sme_sumopa_wide op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_sumopa_wide : public ::mlir::Op<aarch64_sme_sumopa_wide, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<4>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_sumopa_wideAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_sumopa_wideGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.sumopa.wide");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getLhsPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getRhsPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getLhsVector();
  ::mlir::TypedValue<::mlir::VectorType> getRhsVector();
  ::mlir::OpOperand &getLhsPredicateMutable();
  ::mlir::OpOperand &getRhsPredicateMutable();
  ::mlir::OpOperand &getLhsVectorMutable();
  ::mlir::OpOperand &getRhsVectorMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::IntegerAttr tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::IntegerAttr tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, uint32_t tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, uint32_t tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_sumopa_wide)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_sumops_wide declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_sumops_wideGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_sumops_wideGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_sumops_wideGenericAdaptorBase(aarch64_sme_sumops_wide op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_sumops_wideGenericAdaptor : public detail::aarch64_sme_sumops_wideGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_sumops_wideGenericAdaptorBase;
public:
  aarch64_sme_sumops_wideGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_sumops_wideGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_sumops_wideGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_sumops_wide, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_sumops_wide>>>
  aarch64_sme_sumops_wideGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getLhsPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getRhsPredicate() {
    return (*getODSOperands(1).begin());
  }

  ValueT getLhsVector() {
    return (*getODSOperands(2).begin());
  }

  ValueT getRhsVector() {
    return (*getODSOperands(3).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_sumops_wideAdaptor : public aarch64_sme_sumops_wideGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_sumops_wideGenericAdaptor::aarch64_sme_sumops_wideGenericAdaptor;
  aarch64_sme_sumops_wideAdaptor(aarch64_sme_sumops_wide op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_sumops_wide : public ::mlir::Op<aarch64_sme_sumops_wide, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<4>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_sumops_wideAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_sumops_wideGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.sumops.wide");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getLhsPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getRhsPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getLhsVector();
  ::mlir::TypedValue<::mlir::VectorType> getRhsVector();
  ::mlir::OpOperand &getLhsPredicateMutable();
  ::mlir::OpOperand &getRhsPredicateMutable();
  ::mlir::OpOperand &getLhsVectorMutable();
  ::mlir::OpOperand &getRhsVectorMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::IntegerAttr tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::IntegerAttr tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, uint32_t tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, uint32_t tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_sumops_wide)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_umopa_wide declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_umopa_wideGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_umopa_wideGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_umopa_wideGenericAdaptorBase(aarch64_sme_umopa_wide op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_umopa_wideGenericAdaptor : public detail::aarch64_sme_umopa_wideGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_umopa_wideGenericAdaptorBase;
public:
  aarch64_sme_umopa_wideGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_umopa_wideGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_umopa_wideGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_umopa_wide, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_umopa_wide>>>
  aarch64_sme_umopa_wideGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getLhsPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getRhsPredicate() {
    return (*getODSOperands(1).begin());
  }

  ValueT getLhsVector() {
    return (*getODSOperands(2).begin());
  }

  ValueT getRhsVector() {
    return (*getODSOperands(3).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_umopa_wideAdaptor : public aarch64_sme_umopa_wideGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_umopa_wideGenericAdaptor::aarch64_sme_umopa_wideGenericAdaptor;
  aarch64_sme_umopa_wideAdaptor(aarch64_sme_umopa_wide op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_umopa_wide : public ::mlir::Op<aarch64_sme_umopa_wide, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<4>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_umopa_wideAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_umopa_wideGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.umopa.wide");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getLhsPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getRhsPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getLhsVector();
  ::mlir::TypedValue<::mlir::VectorType> getRhsVector();
  ::mlir::OpOperand &getLhsPredicateMutable();
  ::mlir::OpOperand &getRhsPredicateMutable();
  ::mlir::OpOperand &getLhsVectorMutable();
  ::mlir::OpOperand &getRhsVectorMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::IntegerAttr tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::IntegerAttr tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, uint32_t tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, uint32_t tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_umopa_wide)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_umopa_za32 declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_umopa_za32GenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_umopa_za32GenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_umopa_za32GenericAdaptorBase(aarch64_sme_umopa_za32 op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_umopa_za32GenericAdaptor : public detail::aarch64_sme_umopa_za32GenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_umopa_za32GenericAdaptorBase;
public:
  aarch64_sme_umopa_za32GenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_umopa_za32GenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_umopa_za32GenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_umopa_za32, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_umopa_za32>>>
  aarch64_sme_umopa_za32GenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getLhsPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getRhsPredicate() {
    return (*getODSOperands(1).begin());
  }

  ValueT getLhsVector() {
    return (*getODSOperands(2).begin());
  }

  ValueT getRhsVector() {
    return (*getODSOperands(3).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_umopa_za32Adaptor : public aarch64_sme_umopa_za32GenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_umopa_za32GenericAdaptor::aarch64_sme_umopa_za32GenericAdaptor;
  aarch64_sme_umopa_za32Adaptor(aarch64_sme_umopa_za32 op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_umopa_za32 : public ::mlir::Op<aarch64_sme_umopa_za32, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<4>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_umopa_za32Adaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_umopa_za32GenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.umopa.za32");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getLhsPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getRhsPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getLhsVector();
  ::mlir::TypedValue<::mlir::VectorType> getRhsVector();
  ::mlir::OpOperand &getLhsPredicateMutable();
  ::mlir::OpOperand &getRhsPredicateMutable();
  ::mlir::OpOperand &getLhsVectorMutable();
  ::mlir::OpOperand &getRhsVectorMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::IntegerAttr tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::IntegerAttr tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, uint32_t tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, uint32_t tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_umopa_za32)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_umops_wide declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_umops_wideGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_umops_wideGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_umops_wideGenericAdaptorBase(aarch64_sme_umops_wide op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_umops_wideGenericAdaptor : public detail::aarch64_sme_umops_wideGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_umops_wideGenericAdaptorBase;
public:
  aarch64_sme_umops_wideGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_umops_wideGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_umops_wideGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_umops_wide, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_umops_wide>>>
  aarch64_sme_umops_wideGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getLhsPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getRhsPredicate() {
    return (*getODSOperands(1).begin());
  }

  ValueT getLhsVector() {
    return (*getODSOperands(2).begin());
  }

  ValueT getRhsVector() {
    return (*getODSOperands(3).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_umops_wideAdaptor : public aarch64_sme_umops_wideGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_umops_wideGenericAdaptor::aarch64_sme_umops_wideGenericAdaptor;
  aarch64_sme_umops_wideAdaptor(aarch64_sme_umops_wide op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_umops_wide : public ::mlir::Op<aarch64_sme_umops_wide, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<4>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_umops_wideAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_umops_wideGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.umops.wide");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getLhsPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getRhsPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getLhsVector();
  ::mlir::TypedValue<::mlir::VectorType> getRhsVector();
  ::mlir::OpOperand &getLhsPredicateMutable();
  ::mlir::OpOperand &getRhsPredicateMutable();
  ::mlir::OpOperand &getLhsVectorMutable();
  ::mlir::OpOperand &getRhsVectorMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::IntegerAttr tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::IntegerAttr tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, uint32_t tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, uint32_t tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_umops_wide)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_umops_za32 declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_umops_za32GenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_umops_za32GenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_umops_za32GenericAdaptorBase(aarch64_sme_umops_za32 op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_umops_za32GenericAdaptor : public detail::aarch64_sme_umops_za32GenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_umops_za32GenericAdaptorBase;
public:
  aarch64_sme_umops_za32GenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_umops_za32GenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_umops_za32GenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_umops_za32, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_umops_za32>>>
  aarch64_sme_umops_za32GenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getLhsPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getRhsPredicate() {
    return (*getODSOperands(1).begin());
  }

  ValueT getLhsVector() {
    return (*getODSOperands(2).begin());
  }

  ValueT getRhsVector() {
    return (*getODSOperands(3).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_umops_za32Adaptor : public aarch64_sme_umops_za32GenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_umops_za32GenericAdaptor::aarch64_sme_umops_za32GenericAdaptor;
  aarch64_sme_umops_za32Adaptor(aarch64_sme_umops_za32 op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_umops_za32 : public ::mlir::Op<aarch64_sme_umops_za32, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<4>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_umops_za32Adaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_umops_za32GenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.umops.za32");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getLhsPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getRhsPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getLhsVector();
  ::mlir::TypedValue<::mlir::VectorType> getRhsVector();
  ::mlir::OpOperand &getLhsPredicateMutable();
  ::mlir::OpOperand &getRhsPredicateMutable();
  ::mlir::OpOperand &getLhsVectorMutable();
  ::mlir::OpOperand &getRhsVectorMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::IntegerAttr tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::IntegerAttr tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, uint32_t tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, uint32_t tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_umops_za32)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_usmopa_wide declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_usmopa_wideGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_usmopa_wideGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_usmopa_wideGenericAdaptorBase(aarch64_sme_usmopa_wide op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_usmopa_wideGenericAdaptor : public detail::aarch64_sme_usmopa_wideGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_usmopa_wideGenericAdaptorBase;
public:
  aarch64_sme_usmopa_wideGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_usmopa_wideGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_usmopa_wideGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_usmopa_wide, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_usmopa_wide>>>
  aarch64_sme_usmopa_wideGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getLhsPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getRhsPredicate() {
    return (*getODSOperands(1).begin());
  }

  ValueT getLhsVector() {
    return (*getODSOperands(2).begin());
  }

  ValueT getRhsVector() {
    return (*getODSOperands(3).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_usmopa_wideAdaptor : public aarch64_sme_usmopa_wideGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_usmopa_wideGenericAdaptor::aarch64_sme_usmopa_wideGenericAdaptor;
  aarch64_sme_usmopa_wideAdaptor(aarch64_sme_usmopa_wide op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_usmopa_wide : public ::mlir::Op<aarch64_sme_usmopa_wide, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<4>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_usmopa_wideAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_usmopa_wideGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.usmopa.wide");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getLhsPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getRhsPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getLhsVector();
  ::mlir::TypedValue<::mlir::VectorType> getRhsVector();
  ::mlir::OpOperand &getLhsPredicateMutable();
  ::mlir::OpOperand &getRhsPredicateMutable();
  ::mlir::OpOperand &getLhsVectorMutable();
  ::mlir::OpOperand &getRhsVectorMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::IntegerAttr tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::IntegerAttr tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, uint32_t tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, uint32_t tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_usmopa_wide)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_usmops_wide declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_usmops_wideGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_usmops_wideGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_usmops_wideGenericAdaptorBase(aarch64_sme_usmops_wide op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_usmops_wideGenericAdaptor : public detail::aarch64_sme_usmops_wideGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_usmops_wideGenericAdaptorBase;
public:
  aarch64_sme_usmops_wideGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_usmops_wideGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_usmops_wideGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_usmops_wide, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_usmops_wide>>>
  aarch64_sme_usmops_wideGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getLhsPredicate() {
    return (*getODSOperands(0).begin());
  }

  ValueT getRhsPredicate() {
    return (*getODSOperands(1).begin());
  }

  ValueT getLhsVector() {
    return (*getODSOperands(2).begin());
  }

  ValueT getRhsVector() {
    return (*getODSOperands(3).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_usmops_wideAdaptor : public aarch64_sme_usmops_wideGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_usmops_wideGenericAdaptor::aarch64_sme_usmops_wideGenericAdaptor;
  aarch64_sme_usmops_wideAdaptor(aarch64_sme_usmops_wide op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_usmops_wide : public ::mlir::Op<aarch64_sme_usmops_wide, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<4>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_usmops_wideAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_usmops_wideGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.usmops.wide");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::VectorType> getLhsPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getRhsPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getLhsVector();
  ::mlir::TypedValue<::mlir::VectorType> getRhsVector();
  ::mlir::OpOperand &getLhsPredicateMutable();
  ::mlir::OpOperand &getRhsPredicateMutable();
  ::mlir::OpOperand &getLhsVectorMutable();
  ::mlir::OpOperand &getRhsVectorMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::IntegerAttr tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::IntegerAttr tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, uint32_t tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, uint32_t tile_id, ::mlir::Value lhs_predicate, ::mlir::Value rhs_predicate, ::mlir::Value lhs_vector, ::mlir::Value rhs_vector);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_usmops_wide)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_write_horiz declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_write_horizGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_write_horizGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_write_horizGenericAdaptorBase(aarch64_sme_write_horiz op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_write_horizGenericAdaptor : public detail::aarch64_sme_write_horizGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_write_horizGenericAdaptorBase;
public:
  aarch64_sme_write_horizGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_write_horizGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_write_horizGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_write_horiz, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_write_horiz>>>
  aarch64_sme_write_horizGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getTileSliceIndex() {
    return (*getODSOperands(0).begin());
  }

  ValueT getPredicate() {
    return (*getODSOperands(1).begin());
  }

  ValueT getVector() {
    return (*getODSOperands(2).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_write_horizAdaptor : public aarch64_sme_write_horizGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_write_horizGenericAdaptor::aarch64_sme_write_horizGenericAdaptor;
  aarch64_sme_write_horizAdaptor(aarch64_sme_write_horiz op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_write_horiz : public ::mlir::Op<aarch64_sme_write_horiz, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<3>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_write_horizAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_write_horizGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.write.horiz");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::IntegerType> getTileSliceIndex();
  ::mlir::TypedValue<::mlir::VectorType> getPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getVector();
  ::mlir::OpOperand &getTileSliceIndexMutable();
  ::mlir::OpOperand &getPredicateMutable();
  ::mlir::OpOperand &getVectorMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index, ::mlir::Value predicate, ::mlir::Value vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index, ::mlir::Value predicate, ::mlir::Value vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, uint32_t tile_id, ::mlir::Value tile_slice_index, ::mlir::Value predicate, ::mlir::Value vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, uint32_t tile_id, ::mlir::Value tile_slice_index, ::mlir::Value predicate, ::mlir::Value vector);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_write_horiz)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_write_vert declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_write_vertGenericAdaptorBase {
public:
  struct Properties {
    using tile_idTy = ::mlir::IntegerAttr;
    tile_idTy tile_id;

    auto getTileId() {
      auto &propStorage = this->tile_id;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileId(const ::mlir::IntegerAttr &propValue) {
      this->tile_id = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_id == this->tile_id &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_write_vertGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_write_vertGenericAdaptorBase(aarch64_sme_write_vert op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_write_vertGenericAdaptor : public detail::aarch64_sme_write_vertGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_write_vertGenericAdaptorBase;
public:
  aarch64_sme_write_vertGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_write_vertGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_write_vertGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_write_vert, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_write_vert>>>
  aarch64_sme_write_vertGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getTileSliceIndex() {
    return (*getODSOperands(0).begin());
  }

  ValueT getPredicate() {
    return (*getODSOperands(1).begin());
  }

  ValueT getVector() {
    return (*getODSOperands(2).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_write_vertAdaptor : public aarch64_sme_write_vertGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_write_vertGenericAdaptor::aarch64_sme_write_vertGenericAdaptor;
  aarch64_sme_write_vertAdaptor(aarch64_sme_write_vert op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_write_vert : public ::mlir::Op<aarch64_sme_write_vert, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<3>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_write_vertAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_write_vertGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_id")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileIdAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileIdAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.write.vert");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  ::mlir::TypedValue<::mlir::IntegerType> getTileSliceIndex();
  ::mlir::TypedValue<::mlir::VectorType> getPredicate();
  ::mlir::TypedValue<::mlir::VectorType> getVector();
  ::mlir::OpOperand &getTileSliceIndexMutable();
  ::mlir::OpOperand &getPredicateMutable();
  ::mlir::OpOperand &getVectorMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileIdAttr();
  uint32_t getTileId();
  void setTileIdAttr(::mlir::IntegerAttr attr);
  void setTileId(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index, ::mlir::Value predicate, ::mlir::Value vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::IntegerAttr tile_id, ::mlir::Value tile_slice_index, ::mlir::Value predicate, ::mlir::Value vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, uint32_t tile_id, ::mlir::Value tile_slice_index, ::mlir::Value predicate, ::mlir::Value vector);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, uint32_t tile_id, ::mlir::Value tile_slice_index, ::mlir::Value predicate, ::mlir::Value vector);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_write_vert)

namespace mlir {
namespace arm_sme {

//===----------------------------------------------------------------------===//
// ::mlir::arm_sme::aarch64_sme_zero declarations
//===----------------------------------------------------------------------===//

namespace detail {
class aarch64_sme_zeroGenericAdaptorBase {
public:
  struct Properties {
    using tile_maskTy = ::mlir::IntegerAttr;
    tile_maskTy tile_mask;

    auto getTileMask() {
      auto &propStorage = this->tile_mask;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setTileMask(const ::mlir::IntegerAttr &propValue) {
      this->tile_mask = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.tile_mask == this->tile_mask &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  aarch64_sme_zeroGenericAdaptorBase(::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {});

  aarch64_sme_zeroGenericAdaptorBase(aarch64_sme_zero op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes();
  ::mlir::IntegerAttr getTileMaskAttr();
  uint32_t getTileMask();
};
} // namespace detail
template <typename RangeT>
class aarch64_sme_zeroGenericAdaptor : public detail::aarch64_sme_zeroGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::aarch64_sme_zeroGenericAdaptorBase;
public:
  aarch64_sme_zeroGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr, const Properties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  aarch64_sme_zeroGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : aarch64_sme_zeroGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  template <typename LateInst = aarch64_sme_zero, typename = std::enable_if_t<std::is_same_v<LateInst, aarch64_sme_zero>>>
  aarch64_sme_zeroGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class aarch64_sme_zeroAdaptor : public aarch64_sme_zeroGenericAdaptor<::mlir::ValueRange> {
public:
  using aarch64_sme_zeroGenericAdaptor::aarch64_sme_zeroGenericAdaptor;
  aarch64_sme_zeroAdaptor(aarch64_sme_zero op);

  ::mlir::LogicalResult verify(::mlir::Location loc);
};
class aarch64_sme_zero : public ::mlir::Op<aarch64_sme_zero, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::ZeroOperands, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = aarch64_sme_zeroAdaptor;
  template <typename RangeT>
  using GenericAdaptor = aarch64_sme_zeroGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("tile_mask")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTileMaskAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTileMaskAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("arm_sme.intr.zero");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index);
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index);
  static ::mlir::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::mlir::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getTileMaskAttr();
  uint32_t getTileMask();
  void setTileMaskAttr(::mlir::IntegerAttr attr);
  void setTileMask(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::IntegerAttr tile_mask);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::IntegerAttr tile_mask);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, uint32_t tile_mask);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, uint32_t tile_mask);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::mlir::LogicalResult verifyInvariantsImpl();
  ::mlir::LogicalResult verifyInvariants();
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace arm_sme
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::arm_sme::aarch64_sme_zero)


#endif  // GET_OP_CLASSES

